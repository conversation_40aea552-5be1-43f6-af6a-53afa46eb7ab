// Utility functions for managing blob URLs and preventing memory leaks

const blobUrlCache = new Map<string, string>();

/**
 * Create a blob URL and cache it for cleanup
 */
export function createManagedBlobUrl(blob: Blob, key: string): string {
  // Clean up existing URL if it exists
  const existingUrl = blobUrlCache.get(key);
  if (existingUrl) {
    URL.revokeObjectURL(existingUrl);
  }
  
  // Create new URL and cache it
  const url = URL.createObjectURL(blob);
  blobUrlCache.set(key, url);
  
  return url;
}

/**
 * Clean up a specific blob URL
 */
export function revokeManagedBlobUrl(key: string): void {
  const url = blobUrlCache.get(key);
  if (url) {
    URL.revokeObjectURL(url);
    blobUrlCache.delete(key);
  }
}

/**
 * Clean up all managed blob URLs
 */
export function revokeAllManagedBlobUrls(): void {
  blobUrlCache.forEach((url) => {
    URL.revokeObjectURL(url);
  });
  blobUrlCache.clear();
}

/**
 * Get the current blob URL for a key without creating a new one
 */
export function getManagedBlobUrl(key: string): string | undefined {
  return blobUrlCache.get(key);
}

/**
 * Create a temporary blob URL that will be automatically cleaned up after a delay
 */
export function createTemporaryBlobUrl(blob: Blob, delayMs: number = 60000): string {
  const url = URL.createObjectURL(blob);
  
  // Auto-cleanup after delay
  setTimeout(() => {
    URL.revokeObjectURL(url);
  }, delayMs);
  
  return url;
}