# Weld Defect Detection API Documentation

## Overview

The Weld Defect Detection API is a production-ready FastAPI backend that provides comprehensive endpoints for managing weld inspection data, user authentication, and synchronization between offline clients and the server.

**Base URL:** `http://localhost:8000` (development) or `https://your-domain.com` (production)

**API Version:** v1

**Current Version:** 1.0.0

## Authentication

The API uses JWT (JSON Web Tokens) for authentication with Bearer token authorization.

### Authentication Flow

1. **Login** - Obtain access and refresh tokens
2. **Use Access Token** - Include in Authorization header for protected endpoints
3. **Refresh Token** - Use refresh token to get new access token when expired

### Token Configuration

- **Access Token Expiry:** 30 minutes
- **Refresh Token Expiry:** 7 days
- **Algorithm:** HS256

## Quick Start

### 1. Login to get access token

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=inspector1&password=password123"
```

### 2. Use token in subsequent requests

```bash
curl -X GET "http://localhost:8000/api/v1/frames/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Health Check

### Check API Health
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "message": "Weld Defect Detection API is running",
  "version": "1.0.0"
}
```

## Authentication Endpoints

### Login
```http
POST /api/v1/auth/login
```

**Request Body (form-data):**
```
username: string (required)
password: string (required)
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Register New User
```http
POST /api/v1/auth/register
```

**Request Body:**
```json
{
  "username": "new_inspector",
  "email": "<EMAIL>",
  "full_name": "Inspector Name",
  "password": "secure_password123",
  "role": "inspector"
}
```

**Response:**
```json
{
  "user_id": "uuid-string",
  "username": "new_inspector",
  "full_name": "Inspector Name",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": **********,
  "last_login": null
}
```

### Get Current User
```http
GET /api/v1/auth/me
```

**Headers:**
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Response:**
```json
{
  "user_id": "uuid-string",
  "username": "inspector1",
  "full_name": "Inspector One",
  "email": "<EMAIL>",
  "role": "inspector",
  "is_active": true,
  "created_at": **********,
  "last_login": **********
}
```

### Refresh Access Token
```http
POST /api/v1/auth/refresh
```

**Request Body:**
```json
{
  "refresh_token": "your_refresh_token_here"
}
```

### Change Password
```http
POST /api/v1/auth/change-password
```

**Request Body:**
```json
{
  "current_password": "old_password",
  "new_password": "new_secure_password"
}
```

### Forgot Password
```http
POST /api/v1/auth/forgot-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### Reset Password
```http
POST /api/v1/auth/reset-password
```

**Request Body:**
```json
{
  "token": "reset_token_from_email",
  "new_password": "new_secure_password"
}
```

## Frame Management Endpoints

Frames represent inspection sessions with metadata about the weld inspection context.

### Create Frame
```http
POST /api/v1/frames/
```

**Request Body:**
```json
{
  "model_number": "WELD-001",
  "machine_serial_number": "SN12345",
  "inspector_name": "Inspector Name",
  "status": "active",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "Morning",
    "temperature": 25.5
  }
}
```

**Response:**
```json
{
  "frame_id": "uuid-string",
  "model_number": "WELD-001",
  "machine_serial_number": "SN12345",
  "inspector_name": "Inspector Name",
  "status": "active",
  "metadata": {
    "location": "Factory Floor A",
    "shift": "Morning",
    "temperature": 25.5
  },
  "creation_timestamp": **********,
  "last_modified_timestamp": **********,
  "capture_count": 0,
  "sync_status": "synced",
  "last_synced_at": **********
}
```

### List Frames
```http
GET /api/v1/frames/
```

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Number of records to return (default: 100, max: 1000)
- `inspector_name` (string): Filter by inspector name
- `machine_serial_number` (string): Filter by machine serial number
- `status` (string): Filter by status (active, completed, archived)

**Response:**
```json
{
  "frames": [
    {
      "frame_id": "uuid-string",
      "model_number": "WELD-001",
      "machine_serial_number": "SN12345",
      "inspector_name": "Inspector Name",
      "status": "active",
      "creation_timestamp": **********,
      "last_modified_timestamp": **********,
      "capture_count": 5,
      "sync_status": "synced"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 100
}
```

### Get Frame by ID
```http
GET /api/v1/frames/{frame_id}
```

### Update Frame
```http
PUT /api/v1/frames/{frame_id}
```

**Request Body:**
```json
{
  "status": "completed",
  "metadata": {
    "completed_at": "2023-12-27T10:30:00Z",
    "notes": "Inspection completed successfully"
  }
}
```

### Delete Frame
```http
DELETE /api/v1/frames/{frame_id}
```

**Response:**
```json
{
  "message": "Frame deleted successfully"
}
```

### Get Frame with Captures
```http
GET /api/v1/frames/{frame_id}/details
```

### Search Frames
```http
GET /api/v1/frames/search/
```

**Query Parameters:**
- `q` (string, required): Search query (searches model number, machine serial, inspector name)
- `skip` (int): Skip records (default: 0)
- `limit` (int): Limit results (default: 100)

## Capture Management Endpoints

Captures represent individual weld inspection images with detection results.

### Create Capture
```http
POST /api/v1/captures/
```

**Request Body:**
```json
{
  "frame_id": "uuid-string",
  "detection_results": [
    {
      "id": "detection-1",
      "class": "crack",
      "confidence": 0.95,
      "bbox": {
        "x1": 100,
        "y1": 150,
        "x2": 200,
        "y2": 250
      }
    }
  ],
  "original_image_data": "base64_encoded_image_data",
  "processed_image_data": "base64_encoded_processed_image_data"
}
```

**Response:**
```json
{
  "capture_id": "uuid-string",
  "frame_id": "uuid-string",
  "detection_results": [...],
  "capture_timestamp": **********,
  "sync_status": "synced",
  "sync_version": 1,
  "has_original_image": true,
  "has_processed_image": true,
  "has_thumbnail": false,
  "image_sizes": {
    "original": 1024576,
    "processed": 987654
  }
}
```

### Get Capture
```http
GET /api/v1/captures/{capture_id}
```

### Update Capture
```http
PUT /api/v1/captures/{capture_id}
```

**Query Parameters:**
- `sync_version` (int): Expected sync version for optimistic locking

**Request Body:**
```json
{
  "detection_results": [
    {
      "id": "detection-1-updated",
      "class": "crack",
      "confidence": 0.98,
      "bbox": {
        "x1": 105,
        "y1": 155,
        "x2": 205,
        "y2": 255
      }
    }
  ]
}
```

### Delete Capture
```http
DELETE /api/v1/captures/{capture_id}
```

### List Captures
```http
GET /api/v1/captures/
```

**Query Parameters:**
- `frame_id` (string): Filter by frame ID
- `sync_status` (string): Filter by sync status (synced, pending, conflict)
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20, max: 100)
- `sort_by` (string): Sort field (default: capture_timestamp)
- `sort_order` (string): Sort order (asc/desc, default: desc)

### Get Frame Captures
```http
GET /api/v1/captures/frames/{frame_id}/captures
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20, max: 100)
- `sort_by` (string): Sort field (default: capture_timestamp)
- `sort_order` (string): Sort order (asc/desc, default: desc)

### Capture Storage Operations

#### Get Storage Info
```http
GET /api/v1/captures/{capture_id}/storage
```

#### Regenerate Thumbnails
```http
POST /api/v1/captures/{capture_id}/thumbnails
```

#### Get Storage Metrics
```http
GET /api/v1/captures/storage/metrics
```

## Synchronization Endpoints

The sync endpoints handle offline-first synchronization between clients and the server.

### Sync Single Frame
```http
POST /api/v1/sync/frame
```

**Request Body:**
```json
{
  "operation_type": "create",
  "object_type": "frame",
  "object_id": "client-frame-id",
  "frame_data": {
    "model_number": "WELD-001",
    "machine_serial_number": "SN12345",
    "inspector_name": "Inspector Name",
    "status": "active",
    "creation_timestamp": **********,
    "last_modified_timestamp": **********,
    "metadata": {}
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Frame synced successfully",
  "object_id": "client-frame-id",
  "object_type": "frame",
  "server_object_id": "server-uuid",
  "conflicts": null
}
```

### Sync Single Capture
```http
POST /api/v1/sync/capture
```

**Request Body (multipart/form-data):**
```
operation_type: create
object_type: capture
object_id: client-capture-id
frame_id: frame-uuid
capture_data: {"detection_results": [...], "capture_timestamp": **********}
original_image: file (optional)
processed_image: file (optional)
thumbnail_image: file (optional)
```

### Batch Sync
```http
POST /api/v1/sync/batch
```

**Request Body:**
```json
{
  "requests": [
    {
      "operation_type": "create",
      "object_type": "frame",
      "object_id": "client-frame-1",
      "frame_data": {...}
    },
    {
      "operation_type": "create",
      "object_type": "capture",
      "object_id": "client-capture-1",
      "frame_id": "frame-id",
      "capture_data": {...}
    }
  ],
  "client_id": "client-identifier"
}
```

**Response:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Frame synced successfully",
      "object_id": "client-frame-1",
      "object_type": "frame"
    },
    {
      "success": true,
      "message": "Capture synced successfully",
      "object_id": "client-capture-1",
      "object_type": "capture"
    }
  ],
  "total_requested": 2,
  "successful": 2,
  "failed": 0,
  "errors": null
}
```

### Sync Health Check
```http
GET /api/v1/sync/health
```

### Sync Statistics
```http
GET /api/v1/sync/stats
```

## Reports Endpoints

### Generate PDF Report
```http
GET /api/v1/reports/frame/{frame_id}/pdf
```

**Response:** Binary PDF file with appropriate headers

### Get Frame Summary
```http
GET /api/v1/reports/frame/{frame_id}/summary
```

**Response:**
```json
{
  "frame_id": "uuid-string",
  "model_number": "WELD-001",
  "machine_serial_number": "SN12345",
  "inspector_name": "Inspector Name",
  "capture_count": 10,
  "defect_count": 3,
  "creation_timestamp": **********,
  "completion_timestamp": **********,
  "status": "completed"
}
```

## User Management Endpoints (Admin Only)

### List Users
```http
GET /api/v1/users/
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 20, max: 100)
- `role` (string): Filter by role (admin, inspector)
- `active_only` (bool): Show only active users (default: true)

### Get User Profile
```http
GET /api/v1/users/me
```

### Update User Profile
```http
PUT /api/v1/users/me
```

### Get User by ID (Admin)
```http
GET /api/v1/users/{user_id}
```

### Update User by ID (Admin)
```http
PUT /api/v1/users/{user_id}
```

### Delete User (Admin)
```http
DELETE /api/v1/users/{user_id}
```

### Deactivate User (Admin)
```http
POST /api/v1/users/{user_id}/deactivate
```

## Error Handling

### HTTP Status Codes

- **200 OK** - Request successful
- **201 Created** - Resource created successfully
- **204 No Content** - Request successful, no content to return
- **400 Bad Request** - Invalid request data
- **401 Unauthorized** - Authentication required or invalid
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Resource not found
- **409 Conflict** - Sync conflict or duplicate resource
- **422 Unprocessable Entity** - Validation error
- **500 Internal Server Error** - Server error

### Error Response Format

```json
{
  "detail": "Error message description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "field": "field_name"
}
```

### Common Error Scenarios

#### Authentication Errors
```json
{
  "detail": "Could not validate credentials",
  "headers": {"WWW-Authenticate": "Bearer"}
}
```

#### Validation Errors
```json
{
  "detail": [
    {
      "loc": ["body", "field_name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

#### Sync Conflicts
```json
{
  "detail": "Sync conflict detected",
  "conflicts": ["field1", "field2"]
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authentication endpoints:** 5 requests per minute per IP
- **General endpoints:** 100 requests per minute per authenticated user
- **File upload endpoints:** 10 requests per minute per user

## Data Models

### Frame Schema
```json
{
  "frame_id": "string (UUID)",
  "model_number": "string",
  "machine_serial_number": "string", 
  "inspector_name": "string",
  "status": "active | completed | archived",
  "creation_timestamp": "integer (Unix timestamp)",
  "last_modified_timestamp": "integer (Unix timestamp)",
  "capture_count": "integer",
  "metadata": "object (JSON)",
  "sync_status": "synced | pending | conflict",
  "last_synced_at": "integer (Unix timestamp)"
}
```

### Capture Schema
```json
{
  "capture_id": "string (UUID)",
  "frame_id": "string (UUID)",
  "capture_timestamp": "integer (Unix timestamp)",
  "detection_results": [
    {
      "id": "string",
      "class": "string",
      "confidence": "float (0.0-1.0)",
      "bbox": {
        "x1": "float",
        "y1": "float", 
        "x2": "float",
        "y2": "float"
      }
    }
  ],
  "sync_status": "synced | pending | conflict",
  "sync_version": "integer",
  "has_original_image": "boolean",
  "has_processed_image": "boolean",
  "has_thumbnail": "boolean",
  "image_sizes": {
    "original": "integer (bytes)",
    "processed": "integer (bytes)",
    "thumbnail": "integer (bytes)"
  }
}
```

### User Schema
```json
{
  "user_id": "string (UUID)",
  "username": "string",
  "full_name": "string",
  "email": "string",
  "role": "admin | inspector",
  "is_active": "boolean",
  "created_at": "integer (Unix timestamp)",
  "last_login": "integer (Unix timestamp)"
}
```

## Pagination

List endpoints support pagination with the following pattern:

**Request:**
```http
GET /api/v1/frames/?skip=20&limit=10
```

**Response:**
```json
{
  "frames": [...],
  "total": 100,
  "page": 3,
  "limit": 10,
  "has_next": true,
  "has_previous": true
}
```

## File Upload Constraints

- **Maximum file size:** 10MB per image
- **Supported formats:** JPEG, PNG, WebP
- **Maximum detections per capture:** 100
- **Concurrent uploads:** 5 per user

## Development and Testing

### Default Users
The system creates default users on startup:

- **Admin:** `admin` / `admin123`
- **Inspector:** `inspector1` / `password123`

### Test Script
Run the provided test script to verify API functionality:

```bash
cd backend
uv run python test_api.py
```

### Development Server
```bash
cd backend
uv run uvicorn app.main:app --reload
```

## Production Considerations

### Security
- Use strong SECRET_KEY (minimum 32 characters)
- Enable HTTPS in production
- Configure proper CORS origins
- Implement rate limiting
- Use secure password policies

### Performance
- Database connection pooling
- Image compression enabled
- Indexed database queries
- Async request handling

### Monitoring
- Health check endpoints available
- Structured logging
- Error tracking
- Performance metrics

## CORS Configuration

The API supports CORS with the following default origins:
- `http://localhost:3000` (Frontend development)
- `http://localhost:3001` (Alternative frontend port)

Configure additional origins via the `ALLOWED_ORIGINS` environment variable.

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SECRET_KEY` | JWT signing key | `dev-secret-key-for-development-only-32-chars` |
| `DATABASE_URL` | Database connection string | `sqlite+aiosqlite:///./weld_detection.db` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `["http://localhost:3000", "http://localhost:3001"]` |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | Access token expiry | `30` |
| `IMAGE_COMPRESSION_QUALITY` | JPEG compression quality | `85` |
| `MAX_IMAGE_SIZE_MB` | Maximum image size | `10` |

## API Versioning

The API uses URL path versioning (`/api/v1/`). Future versions will maintain backward compatibility where possible.

## Support

For issues and questions:
1. Check the health endpoints for service status
2. Review error messages and status codes
3. Consult the test scripts for usage examples
4. Refer to the backend logs for detailed error information

---
