'use client';

import { useEffect, useCallback } from 'react';
import { useDebounce } from './useDebounce';

/**
 * Custom hook for watching IndexedDB changes across tabs using BroadcastChannel
 * @param onDataChange - Callback when data changes are detected
 * @param options - Configuration options
 */
export function useIndexedDBWatcher(
  onDataChange: () => void,
  options: {
    channel?: string;
    debounceMs?: number;
    enabled?: boolean;
  } = {}
) {
  const { 
    channel = 'weld-capture-updates', 
    debounceMs = 200, 
    enabled = true 
  } = options;
  
  // Debounce the data change callback
  const debouncedDataChange = useDebounce(onDataChange, debounceMs);
  
  // Stable callback for data changes
  const stableDataChange = useCallback(() => {
    if (enabled) {
      debouncedDataChange();
    }
  }, [debouncedDataChange, enabled]);
  
  // Function to broadcast changes to other tabs
  const broadcastChange = useCallback((changeType: string, data?: unknown) => {
    if (!enabled || typeof window === 'undefined') return;
    
    try {
      const broadcastChannel = new BroadcastChannel(channel);
      broadcastChannel.postMessage({
        type: changeType,
        timestamp: Date.now(),
        data
      });
      broadcastChannel.close();
    } catch (error) {
      console.warn('Failed to broadcast change:', error);
    }
  }, [channel, enabled]);
  
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;
    
    let broadcastChannel: BroadcastChannel;
    
    try {
      // Create broadcast channel for listening to changes
      broadcastChannel = new BroadcastChannel(channel);
      
      // Handle messages from other tabs
      const handleMessage = (event: MessageEvent) => {
        // Only respond to capture-related changes
        if (event.data?.type?.includes('capture')) {
          stableDataChange();
        }
      };
      
      broadcastChannel.addEventListener('message', handleMessage);
      
      // Also listen for storage events as fallback
      const handleStorage = (event: StorageEvent) => {
        if (event.key && event.key.includes('capture')) {
          stableDataChange();
        }
      };
      
      window.addEventListener('storage', handleStorage);
      
      return () => {
        broadcastChannel?.removeEventListener('message', handleMessage);
        broadcastChannel?.close();
        window.removeEventListener('storage', handleStorage);
      };
    } catch {
      console.warn('BroadcastChannel not supported, falling back to storage events');
      
      // Fallback to storage events only
      const handleStorage = (event: StorageEvent) => {
        if (event.key && event.key.includes('capture')) {
          stableDataChange();
        }
      };
      
      window.addEventListener('storage', handleStorage);
      
      return () => {
        window.removeEventListener('storage', handleStorage);
      };
    }
  }, [channel, stableDataChange, enabled]);
  
  return { broadcastChange };
}