from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Literal
from enum import Enum


class ReportGranularity(str, Enum):
    """Time granularity options for trend reports."""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"


class ExportFormat(str, Enum):
    """Export format options."""
    JSON = "json"
    CSV = "csv"
    PDF = "pdf"


class ReportFilters(BaseModel):
    """Common filters for reports."""
    inspector_id: Optional[str] = Field(None, description="Filter by inspector ID")
    machine_serial: Optional[str] = Field(None, description="Filter by machine serial number")
    start_date: Optional[int] = Field(None, description="Start date timestamp (milliseconds)")
    end_date: Optional[int] = Field(None, description="End date timestamp (milliseconds)")
    
    @validator('start_date', 'end_date')
    def validate_timestamps(cls, v):
        """Validate timestamp format."""
        if v is not None and (v < 0 or v > 9999999999999):  # Reasonable timestamp range
            raise ValueError("Invalid timestamp")
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """Ensure end_date is after start_date."""
        if v is not None and values.get('start_date') is not None:
            if v <= values['start_date']:
                raise ValueError("End date must be after start date")
        return v


class DetectionSummaryResponse(BaseModel):
    """Response model for detection summary statistics."""
    total_frames: int = Field(..., description="Total number of frames")
    total_captures: int = Field(..., description="Total number of captures")
    total_detections: int = Field(..., description="Total number of detections")
    avg_detections_per_capture: float = Field(..., description="Average detections per capture")


class ClassDistributionItem(BaseModel):
    """Single item in class distribution."""
    class_name: str = Field(alias="class", description="Object class name")
    count: int = Field(..., description="Number of detections")
    avg_confidence: float = Field(..., description="Average confidence score")
    
    class Config:
        allow_population_by_field_name = True


class ClassDistributionResponse(BaseModel):
    """Response model for detection class distribution."""
    distribution: List[ClassDistributionItem] = Field(..., description="Class distribution data")
    total_classes: int = Field(..., description="Total number of different classes")


class TrendDataPoint(BaseModel):
    """Single data point in trend analysis."""
    timestamp: int = Field(..., description="Timestamp (milliseconds)")
    date: str = Field(..., description="ISO date string")
    capture_count: int = Field(..., description="Number of captures in this time period")
    detection_count: int = Field(..., description="Number of detections in this time period")


class TrendAnalysisResponse(BaseModel):
    """Response model for trend analysis."""
    granularity: ReportGranularity = Field(..., description="Time granularity used")
    data_points: List[TrendDataPoint] = Field(..., description="Trend data points")
    total_periods: int = Field(..., description="Total number of time periods")


class InspectorPerformanceItem(BaseModel):
    """Single inspector performance item."""
    inspector_id: str = Field(..., description="Inspector user ID")
    inspector_name: str = Field(..., description="Inspector full name")
    total_frames: int = Field(..., description="Total frames created")
    total_captures: int = Field(..., description="Total captures taken")
    total_detections: int = Field(..., description="Total detections made")
    avg_detections_per_capture: float = Field(..., description="Average detections per capture")


class InspectorPerformanceResponse(BaseModel):
    """Response model for inspector performance."""
    inspectors: List[InspectorPerformanceItem] = Field(..., description="Inspector performance data")
    total_inspectors: int = Field(..., description="Total number of inspectors")


class MachineStatisticsItem(BaseModel):
    """Single machine statistics item."""
    machine_serial_number: str = Field(..., description="Machine serial number")
    model_number: str = Field(..., description="Machine model number")
    total_frames: int = Field(..., description="Total frames for this machine")
    total_captures: int = Field(..., description="Total captures for this machine")
    total_detections: int = Field(..., description="Total detections for this machine")
    last_activity: int = Field(..., description="Last activity timestamp")
    last_activity_date: str = Field(..., description="Last activity date (ISO format)")


class MachineStatisticsResponse(BaseModel):
    """Response model for machine statistics."""
    machines: List[MachineStatisticsItem] = Field(..., description="Machine statistics data")
    total_machines: int = Field(..., description="Total number of machines")


class SyncStatusCounts(BaseModel):
    """Sync status counts for frames or captures."""
    synced: int = Field(..., description="Number of synced items")
    pending: int = Field(..., description="Number of pending items")
    conflict: int = Field(..., description="Number of items with conflicts")


class SyncStatisticsResponse(BaseModel):
    """Response model for sync statistics."""
    frames: SyncStatusCounts = Field(..., description="Frame sync statistics")
    captures: SyncStatusCounts = Field(..., description="Capture sync statistics")


class ConfidenceRanges(BaseModel):
    """Confidence score ranges."""
    high: int = Field(..., description="High confidence detections (> 0.8)")
    medium: int = Field(..., description="Medium confidence detections (0.5 - 0.8)")
    low: int = Field(..., description="Low confidence detections (< 0.5)")


class ConfidenceAnalysisResponse(BaseModel):
    """Response model for confidence analysis."""
    total_detections: int = Field(..., description="Total number of detections")
    avg_confidence: float = Field(..., description="Average confidence score")
    min_confidence: float = Field(..., description="Minimum confidence score")
    max_confidence: float = Field(..., description="Maximum confidence score")
    confidence_ranges: ConfidenceRanges = Field(..., description="Confidence score ranges")


class ReportRequest(BaseModel):
    """Request model for generating reports."""
    report_type: Literal[
        "summary", "class_distribution", "trends", "inspector_performance", 
        "machine_statistics", "sync_statistics", "confidence_analysis"
    ] = Field(..., description="Type of report to generate")
    filters: ReportFilters = Field(default_factory=ReportFilters, description="Report filters")
    granularity: Optional[ReportGranularity] = Field(None, description="Time granularity (for trend reports)")
    export_format: ExportFormat = Field(ExportFormat.JSON, description="Export format")


class ExportRequest(BaseModel):
    """Request model for exporting reports."""
    report_type: str = Field(..., description="Type of report to export")
    filters: ReportFilters = Field(default_factory=ReportFilters, description="Report filters")
    format: ExportFormat = Field(..., description="Export format")
    include_images: bool = Field(False, description="Include thumbnail images (PDF only)")
    granularity: Optional[ReportGranularity] = Field(None, description="Time granularity (for trend reports)")


class ExportResponse(BaseModel):
    """Response model for export operations."""
    success: bool = Field(..., description="Whether export was successful")
    download_url: Optional[str] = Field(None, description="Download URL for the exported file")
    filename: str = Field(..., description="Generated filename")
    format: ExportFormat = Field(..., description="Export format used")
    size_bytes: Optional[int] = Field(None, description="File size in bytes")
    expires_at: Optional[int] = Field(None, description="Expiration timestamp for download URL")


class ComprehensiveReportResponse(BaseModel):
    """Comprehensive report combining multiple analytics."""
    summary: DetectionSummaryResponse = Field(..., description="Detection summary")
    class_distribution: ClassDistributionResponse = Field(..., description="Class distribution")
    confidence_analysis: ConfidenceAnalysisResponse = Field(..., description="Confidence analysis")
    sync_statistics: SyncStatisticsResponse = Field(..., description="Sync statistics")
    generated_at: int = Field(..., description="Report generation timestamp")
    filters_applied: ReportFilters = Field(..., description="Filters used for this report")


class ReportMetadata(BaseModel):
    """Metadata for reports."""
    generated_at: int = Field(..., description="Report generation timestamp")
    generated_by: str = Field(..., description="User who generated the report")
    filters_applied: ReportFilters = Field(..., description="Filters applied")
    total_processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")
    data_freshness: Optional[int] = Field(None, description="Data freshness timestamp")