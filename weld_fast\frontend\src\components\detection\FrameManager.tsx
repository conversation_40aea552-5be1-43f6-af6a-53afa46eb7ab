// src/components/detection/FrameManager.tsx
import { Clock } from 'lucide-react';
import { useSession } from '@/context/SessionContext';

interface FrameManagerProps {
  frameId: string;
  captureCount: number;
}

export default function FrameManager({ frameId, captureCount }: FrameManagerProps) {
  const { currentFrame } = useSession();
  
  return (
    <div className="space-y-3">
      <div className="flex items-center">
        <div className="text-lg font-medium flex items-center gap-2">
          <Clock size={18} />
          <span>Detection History</span>
        </div>
      </div>
      
      {/* Frame details */}
      <div className="text-xs text-gray-500 dark:text-gray-400">
        <div>Frame ID: {frameId.substring(0, 8)}...</div>
        <div>Captures in this frame: {captureCount}</div>
        {currentFrame && (
          <div>Created: {new Date(currentFrame.creationTimestamp).toLocaleString()}</div>
        )}
      </div>
    </div>
  );
}