from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, desc, asc, select
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
import json

from ..models.database import Frame, Capture, User, FrameStatus, SyncStatus


class AnalyticsService:
    """Service for generating analytics and reports from detection data."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_detection_summary(
        self, 
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get overall detection statistics summary."""
        
        # Build base query using select()
        query = select(
            func.count(Frame.frame_id.distinct()).label('total_frames'),
            func.count(Capture.capture_id).label('total_captures'),
            func.avg(func.json_array_length(Capture.detection_results)).label('avg_detections_per_capture')
        ).select_from(Frame).join(Capture, Frame.frame_id == Capture.frame_id)
        
        # Apply filters
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if machine_serial:
            query = query.filter(Frame.machine_serial_number == machine_serial)
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        result = await self.db.execute(query)
        row = result.first()
        
        # Get total detections count
        detections_query = select(
            func.sum(func.json_array_length(Capture.detection_results)).label('total_detections')
        ).select_from(Frame).join(Capture, Frame.frame_id == Capture.frame_id)
        
        # Apply same filters
        if inspector_id:
            detections_query = detections_query.filter(Frame.inspector_id == inspector_id)
        if machine_serial:
            detections_query = detections_query.filter(Frame.machine_serial_number == machine_serial)
        if start_date:
            detections_query = detections_query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            detections_query = detections_query.filter(Frame.creation_timestamp <= end_date)
        
        detections_result = await self.db.execute(detections_query)
        detections_row = detections_result.first()
        
        return {
            'total_frames': row.total_frames or 0,
            'total_captures': row.total_captures or 0,
            'total_detections': detections_row.total_detections or 0,
            'avg_detections_per_capture': float(row.avg_detections_per_capture or 0)
        }
    
    async def get_detection_class_distribution(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get distribution of detected object classes."""
        
        # Build query for captures using select()
        query = select(Capture).select_from(Frame).join(Capture, Frame.frame_id == Capture.frame_id)
        
        # Apply filters
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if machine_serial:
            query = query.filter(Frame.machine_serial_number == machine_serial)
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        result = await self.db.execute(query)
        captures = result.scalars().all()
        
        # Process detection results to count classes
        class_counts = {}
        confidence_sums = {}
        
        for capture in captures:
            if capture.detection_results:
                for detection in capture.detection_results:
                    class_name = detection.get('class', 'unknown')
                    confidence = detection.get('confidence', 0)
                    
                    if class_name in class_counts:
                        class_counts[class_name] += 1
                        confidence_sums[class_name] += confidence
                    else:
                        class_counts[class_name] = 1
                        confidence_sums[class_name] = confidence
        
        # Convert to list with averages
        distribution = []
        for class_name, count in class_counts.items():
            avg_confidence = confidence_sums[class_name] / count
            distribution.append({
                'class': class_name,
                'count': count,
                'avg_confidence': round(avg_confidence, 3)
            })
        
        # Sort by count descending
        distribution.sort(key=lambda x: x['count'], reverse=True)
        return distribution
    
    async def get_detection_trends(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None,
        granularity: str = 'daily'
    ) -> List[Dict[str, Any]]:
        """Get detection trends over time."""
        
        # Determine time grouping based on granularity
        if granularity == 'hourly':
            # Group by hour (timestamp / 3600000)
            time_bucket = func.floor(Capture.capture_timestamp / 3600000) * 3600000
        elif granularity == 'daily':
            # Group by day (timestamp / 86400000)
            time_bucket = func.floor(Capture.capture_timestamp / 86400000) * 86400000
        elif granularity == 'weekly':
            # Group by week (timestamp / 604800000)
            time_bucket = func.floor(Capture.capture_timestamp / 604800000) * 604800000
        else:
            # Default to daily
            time_bucket = func.floor(Capture.capture_timestamp / 86400000) * 86400000
        
        # Build query
        query = self.db.query(
            time_bucket.label('time_bucket'),
            func.count(Capture.capture_id).label('capture_count'),
            func.sum(func.json_array_length(Capture.detection_results)).label('detection_count')
        ).join(Frame, Capture.frame_id == Frame.frame_id)
        
        # Apply filters
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if machine_serial:
            query = query.filter(Frame.machine_serial_number == machine_serial)
        if start_date:
            query = query.filter(Capture.capture_timestamp >= start_date)
        if end_date:
            query = query.filter(Capture.capture_timestamp <= end_date)
        
        # Group and order
        query = query.group_by(time_bucket).order_by(time_bucket)
        
        results = await query.all()
        
        trends = []
        for result in results:
            trends.append({
                'timestamp': int(result.time_bucket),
                'date': datetime.fromtimestamp(result.time_bucket / 1000, timezone.utc).isoformat(),
                'capture_count': result.capture_count,
                'detection_count': result.detection_count or 0
            })
        
        return trends
    
    async def get_inspector_performance(
        self,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get performance metrics by inspector."""
        
        query = self.db.query(
            User.user_id,
            User.full_name,
            func.count(Frame.frame_id.distinct()).label('total_frames'),
            func.count(Capture.capture_id).label('total_captures'),
            func.sum(func.json_array_length(Capture.detection_results)).label('total_detections'),
            func.avg(func.json_array_length(Capture.detection_results)).label('avg_detections_per_capture')
        ).join(Frame, User.user_id == Frame.inspector_id)\
         .join(Capture, Frame.frame_id == Capture.frame_id)
        
        # Apply date filters
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        query = query.group_by(User.user_id, User.full_name)\
                     .order_by(desc('total_captures'))
        
        results = await query.all()
        
        performance = []
        for result in results:
            performance.append({
                'inspector_id': result.user_id,
                'inspector_name': result.full_name,
                'total_frames': result.total_frames,
                'total_captures': result.total_captures,
                'total_detections': result.total_detections or 0,
                'avg_detections_per_capture': round(float(result.avg_detections_per_capture or 0), 2)
            })
        
        return performance
    
    async def get_machine_statistics(
        self,
        inspector_id: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get statistics by machine serial number."""
        
        query = self.db.query(
            Frame.machine_serial_number,
            Frame.model_number,
            func.count(Frame.frame_id.distinct()).label('total_frames'),
            func.count(Capture.capture_id).label('total_captures'),
            func.sum(func.json_array_length(Capture.detection_results)).label('total_detections'),
            func.max(Frame.last_modified_timestamp).label('last_activity')
        ).join(Capture, Frame.frame_id == Capture.frame_id)
        
        # Apply filters
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        query = query.group_by(Frame.machine_serial_number, Frame.model_number)\
                     .order_by(desc('total_captures'))
        
        results = await query.all()
        
        machines = []
        for result in results:
            machines.append({
                'machine_serial_number': result.machine_serial_number,
                'model_number': result.model_number,
                'total_frames': result.total_frames,
                'total_captures': result.total_captures,
                'total_detections': result.total_detections or 0,
                'last_activity': result.last_activity,
                'last_activity_date': datetime.fromtimestamp(result.last_activity / 1000, timezone.utc).isoformat()
            })
        
        return machines
    
    async def get_sync_statistics(
        self,
        inspector_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get synchronization status statistics."""
        
        # Frame sync stats
        frame_query = self.db.query(
            Frame.sync_status,
            func.count(Frame.frame_id).label('count')
        )
        
        if inspector_id:
            frame_query = frame_query.filter(Frame.inspector_id == inspector_id)
        
        frame_stats = await frame_query.group_by(Frame.sync_status).all()
        
        # Capture sync stats
        capture_query = self.db.query(
            Capture.sync_status,
            func.count(Capture.capture_id).label('count')
        ).join(Frame, Capture.frame_id == Frame.frame_id)
        
        if inspector_id:
            capture_query = capture_query.filter(Frame.inspector_id == inspector_id)
        
        capture_stats = await capture_query.group_by(Capture.sync_status).all()
        
        # Format results
        frame_sync = {stat.sync_status.value: stat.count for stat in frame_stats}
        capture_sync = {stat.sync_status.value: stat.count for stat in capture_stats}
        
        return {
            'frames': {
                'synced': frame_sync.get('synced', 0),
                'pending': frame_sync.get('pending', 0),
                'conflict': frame_sync.get('conflict', 0)
            },
            'captures': {
                'synced': capture_sync.get('synced', 0),
                'pending': capture_sync.get('pending', 0),
                'conflict': capture_sync.get('conflict', 0)
            }
        }
    
    async def get_confidence_analysis(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> Dict[str, Any]:
        """Analyze detection confidence scores."""
        
        # Build query for captures
        query = self.db.query(Capture).join(Frame, Capture.frame_id == Frame.frame_id)
        
        # Apply filters
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if machine_serial:
            query = query.filter(Frame.machine_serial_number == machine_serial)
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        captures = await query.all()
        
        # Process confidence scores
        all_confidences = []
        for capture in captures:
            if capture.detection_results:
                for detection in capture.detection_results:
                    confidence = detection.get('confidence', 0)
                    all_confidences.append(confidence)
        
        if not all_confidences:
            return {
                'total_detections': 0,
                'avg_confidence': 0,
                'min_confidence': 0,
                'max_confidence': 0,
                'confidence_ranges': {
                    'high': 0,    # > 0.8
                    'medium': 0,  # 0.5 - 0.8
                    'low': 0      # < 0.5
                }
            }
        
        # Calculate statistics
        avg_confidence = sum(all_confidences) / len(all_confidences)
        min_confidence = min(all_confidences)
        max_confidence = max(all_confidences)
        
        # Categorize confidence ranges
        high_confidence = sum(1 for c in all_confidences if c > 0.8)
        medium_confidence = sum(1 for c in all_confidences if 0.5 <= c <= 0.8)
        low_confidence = sum(1 for c in all_confidences if c < 0.5)
        
        return {
            'total_detections': len(all_confidences),
            'avg_confidence': round(avg_confidence, 3),
            'min_confidence': round(min_confidence, 3),
            'max_confidence': round(max_confidence, 3),
            'confidence_ranges': {
                'high': high_confidence,
                'medium': medium_confidence,
                'low': low_confidence
            }
        }