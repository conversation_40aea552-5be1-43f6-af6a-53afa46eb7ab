# Weld Defect Detection System - Frontend

**Advanced AI-Powered Web Application for Real-Time Weld Defect Detection**

This is the frontend implementation of the Weld Defect Detection System, built with cutting-edge technologies to provide real-time object detection capabilities with an offline-first architecture.

## 🚀 Project Overview

A production-ready web application that uses client-side AI (YOLOv8 + TensorFlow.js) to detect defects in welds through camera capture, with comprehensive offline support and synchronization capabilities.

### ✨ Key Features

- **🤖 Real-Time AI Detection:** YOLOv8 object detection using TensorFlow.js
- **📷 Professional Camera Interface:** WebRTC integration with capture controls
- **💾 Offline-First Architecture:** IndexedDB storage with background synchronization
- **🔐 Advanced Authentication:** JWT-based security with role management
- **🔄 Intelligent Sync:** Conflict resolution and retry logic
- **📱 Cross-Platform:** Responsive design for desktop and mobile
- **⚡ Performance Optimized:** GPU acceleration and memory management

## 🛠️ Technical Stack

### Core Technologies
- **Framework:** Next.js 15.3.3 with App Router
- **Frontend:** React 19.0.0, TypeScript 5
- **Styling:** Tailwind CSS v4 with custom components
- **AI/ML:** TensorFlow.js 4.22.0 with WebGL backend

### UI Components
- **Radix UI:** Accessible component primitives
- **Lucide React:** Modern icon library
- **shadcn/ui patterns:** Professional component architecture

### Data & Storage
- **Database:** IndexedDB with `idb` wrapper for offline-first storage
- **Sync:** Background synchronization with conflict resolution
- **State Management:** React Context API with sophisticated patterns

## 🏗️ Architecture

### Offline-First Design
```
User Interaction → IndexedDB (Local) → Background Sync → Server
                     ↓
               Immediate Response
```

### Key Components
- **Authentication System:** Complete JWT flow with role-based access
- **Session Management:** Create and manage detection sessions
- **Camera Interface:** Real-time capture with AI processing
- **Detection Engine:** YOLOv8 inference with visualization
- **Sync System:** Background synchronization with retry logic

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Modern browser with WebRTC support

### Installation & Development

```bash
# Install dependencies
npm install

# Run development server with Turbopack
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### Environment Setup

The application works out of the box with default configurations. For backend integration, ensure the FastAPI backend is running on the expected port.

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 App Router
│   ├── page.tsx           # Home page with session management
│   ├── detection/         # Main detection interface
│   ├── login/             # Authentication flows
│   └── layout.tsx         # Root layout with providers
├── components/            # Reusable UI components
│   ├── auth/              # Authentication components
│   ├── detection/         # Detection interface components
│   ├── home/              # Home page components
│   └── ui/                # Base UI components (shadcn/ui)
├── context/               # React Context providers
│   ├── AuthContext.tsx    # Global authentication state
│   └── SessionContext.tsx # Session management state
├── lib/                   # Core utilities and services
│   ├── db/                # IndexedDB operations and schemas
│   ├── detection/         # AI detection utilities
│   ├── auth/              # Authentication utilities
│   └── sync/              # Synchronization logic
└── types/                 # TypeScript type definitions
```

## 🎯 Core Features

### 🔐 Authentication System
- **Registration:** Role-based user registration (admin/inspector)
- **Login:** JWT authentication with refresh tokens
- **Profile Management:** User profile editing and password changes
- **Route Protection:** Role-based access control

### 📷 Detection Interface
- **Camera Capture:** Professional WebRTC camera interface
- **Real-Time AI:** YOLOv8 object detection with live visualization
- **Session Management:** Organize captures by detection sessions
- **History Panel:** Browse and manage capture history
- **Sync Status:** Real-time synchronization indicators

### 💾 Data Management
- **IndexedDB Schema:** Sophisticated offline storage
  - `frames`: Detection session metadata
  - `captures`: Images and detection results
  - `syncQueue`: Background sync operations
- **Cross-Tab Sync:** Real-time updates across browser tabs
- **Conflict Resolution:** User-guided resolution for sync conflicts

### 🔄 Synchronization
- **Background Sync:** Automatic synchronization when online
- **Retry Logic:** Exponential backoff with jitter
- **Conflict Detection:** Advanced conflict resolution strategies
- **Offline Support:** Full functionality without internet connection

## 🧪 Development

### Available Scripts

- **`npm run dev`** - Development server with hot reload
- **`npm run build`** - Production build with optimization
- **`npm run start`** - Production server
- **`npm run lint`** - Code linting with ESLint

### Code Quality
- **TypeScript:** Full type safety throughout the application
- **ESLint:** Code quality and consistency enforcement
- **Modern React:** React 19 with latest patterns and hooks

### Performance Optimization
- **TensorFlow.js:** GPU acceleration with WebGL backend
- **IndexedDB:** Efficient local storage with indexing
- **Virtual Scrolling:** Performance optimization for large datasets
- **Image Optimization:** Compression and multi-size thumbnails

## 🔧 Configuration

### AI Model Configuration
The application uses YOLOv8n model files located in:
```
public/models/yolov8n_web_model/
├── model.json
└── group1-shard[1-4]of4.bin
```

### Database Configuration
IndexedDB is automatically initialized with the required schema. No additional configuration needed.

### Sync Configuration
Background sync operates automatically with intelligent scheduling based on:
- Network connectivity status
- User activity patterns
- Data priority levels

## 🚀 Production Deployment

### Build Optimization
- **Next.js 15:** Latest optimizations and Turbopack integration
- **Asset Optimization:** Automatic code splitting and optimization
- **Caching Strategy:** Intelligent caching for static and dynamic content

### Performance Features
- **Offline-First:** Works completely offline after initial load
- **Progressive Loading:** Intelligent data loading and caching
- **Cross-Browser:** Tested on Chrome, Firefox, Safari, Edge
- **Mobile Optimized:** Responsive design with touch-friendly interface

## 📚 Documentation

### Key Documentation Files
- **`CLAUDE.md`** - Comprehensive development guide
- **`PROJECT_STATUS.md`** - Current implementation status
- **API Documentation** - Available in component files

### Component Documentation
Each component includes comprehensive TypeScript interfaces and JSDoc comments for easy understanding and maintenance.

## 🔒 Security

- **JWT Authentication:** Secure token-based authentication
- **Role-Based Access:** Admin and inspector role separation
- **CORS Configuration:** Proper cross-origin resource sharing
- **Input Validation:** Comprehensive data validation throughout

## 🌟 Features in Development

- **Upload Mode:** Batch processing for image uploads
- **Live Detection:** Continuous detection for real-time monitoring
- **Export Features:** PDF and CSV report generation
- **Advanced Analytics:** Detailed detection statistics and reporting

## 📱 Browser Support

- **Chrome:** 90+ (Recommended for best performance)
- **Firefox:** 88+
- **Safari:** 14+
- **Edge:** 90+

**Note:** WebRTC and WebGL support required for full functionality.

## 🤝 Contributing

This is a production application. For development:

1. Follow the existing code patterns and TypeScript conventions
2. Maintain the offline-first architecture principles
3. Ensure comprehensive error handling
4. Test across different browsers and devices
5. Run linting before commits: `npm run lint`

## 📄 License

This project is part of the Weld Defect Detection System. See the main project documentation for licensing information.
