// API functions for reports and analytics

import { authF<PERSON>ch<PERSON>son, authFetchBlob } from '@/lib/auth/authenticatedFetch';

// Note: API_BASE_URL no longer needed - handled by authenticatedFetch service

export interface ReportFilters {
  inspector_id?: string;
  machine_serial?: string;
  start_date?: number;
  end_date?: number;
}

export interface DetectionSummary {
  total_frames: number;
  total_captures: number;
  total_detections: number;
  avg_detections_per_capture: number;
}

export interface ClassDistributionItem {
  class: string;
  count: number;
  avg_confidence: number;
}

export interface ClassDistribution {
  distribution: ClassDistributionItem[];
  total_classes: number;
}

export interface TrendDataPoint {
  timestamp: number;
  date: string;
  capture_count: number;
  detection_count: number;
}

export interface TrendAnalysis {
  granularity: 'hourly' | 'daily' | 'weekly';
  data_points: TrendDataPoint[];
  total_periods: number;
}

export interface InspectorPerformanceItem {
  inspector_id: string;
  inspector_name: string;
  total_frames: number;
  total_captures: number;
  total_detections: number;
  avg_detections_per_capture: number;
}

export interface InspectorPerformance {
  inspectors: InspectorPerformanceItem[];
  total_inspectors: number;
}

export interface MachineStatisticsItem {
  machine_serial_number: string;
  model_number: string;
  total_frames: number;
  total_captures: number;
  total_detections: number;
  last_activity: number;
  last_activity_date: string;
}

export interface MachineStatistics {
  machines: MachineStatisticsItem[];
  total_machines: number;
}

export interface SyncStatusCounts {
  synced: number;
  pending: number;
  conflict: number;
}

export interface SyncStatistics {
  frames: SyncStatusCounts;
  captures: SyncStatusCounts;
}

export interface ConfidenceRanges {
  high: number;
  medium: number;
  low: number;
}

export interface ConfidenceAnalysis {
  total_detections: number;
  avg_confidence: number;
  min_confidence: number;
  max_confidence: number;
  confidence_ranges: ConfidenceRanges;
}

export interface ComprehensiveReport {
  summary: DetectionSummary;
  class_distribution: ClassDistribution;
  confidence_analysis: ConfidenceAnalysis;
  sync_statistics: SyncStatistics;
  generated_at: number;
  filters_applied: ReportFilters;
}

// Note: getAuthToken function removed - authentication now handled by authFetch service

// Helper function to build query parameters
function buildQueryParams(filters: ReportFilters, extraParams: Record<string, unknown> = {}): string {
  const params = new URLSearchParams();
  
  if (filters.inspector_id) params.append('inspector_id', filters.inspector_id);
  if (filters.machine_serial) params.append('machine_serial', filters.machine_serial);
  if (filters.start_date) params.append('start_date', filters.start_date.toString());
  if (filters.end_date) params.append('end_date', filters.end_date.toString());
  
  // Add extra parameters
  Object.entries(extraParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });
  
  return params.toString();
}

// Note: authFetchJson function removed - now using authFetchJson directly

// API Functions

export async function getDetectionSummary(filters: ReportFilters = {}): Promise<DetectionSummary> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<DetectionSummary>(`/api/v1/reports/summary?${queryParams}`);
}

export async function getClassDistribution(filters: ReportFilters = {}): Promise<ClassDistribution> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<ClassDistribution>(`/api/v1/reports/class-distribution?${queryParams}`);
}

export async function getDetectionTrends(
  filters: ReportFilters = {},
  granularity: 'hourly' | 'daily' | 'weekly' = 'daily'
): Promise<TrendAnalysis> {
  const queryParams = buildQueryParams(filters, { granularity });
  return authFetchJson<TrendAnalysis>(`/api/v1/reports/trends?${queryParams}`);
}

export async function getInspectorPerformance(filters: Pick<ReportFilters, 'start_date' | 'end_date'> = {}): Promise<InspectorPerformance> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<InspectorPerformance>(`/api/v1/reports/inspector-performance?${queryParams}`);
}

export async function getMachineStatistics(filters: ReportFilters = {}): Promise<MachineStatistics> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<MachineStatistics>(`/api/v1/reports/machine-statistics?${queryParams}`);
}

export async function getSyncStatistics(filters: Pick<ReportFilters, 'inspector_id'> = {}): Promise<SyncStatistics> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<SyncStatistics>(`/api/v1/reports/sync-statistics?${queryParams}`);
}

export async function getConfidenceAnalysis(filters: ReportFilters = {}): Promise<ConfidenceAnalysis> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<ConfidenceAnalysis>(`/api/v1/reports/confidence-analysis?${queryParams}`);
}

export async function getComprehensiveReport(filters: ReportFilters = {}): Promise<ComprehensiveReport> {
  const queryParams = buildQueryParams(filters);
  return authFetchJson<ComprehensiveReport>(`/api/v1/reports/comprehensive?${queryParams}`);
}

// Export functions

export async function exportCsvReport(
  reportType: 'summary' | 'class_distribution' | 'inspector_performance' | 'machine_statistics',
  filters: ReportFilters = {}
): Promise<Blob> {
  const queryParams = buildQueryParams(filters, { report_type: reportType });
  return authFetchBlob(`/api/v1/reports/export/csv?${queryParams}`);
}

export async function exportJsonReport(
  reportType: 'comprehensive' | 'summary' | 'class_distribution' | 'trends' | 'inspector_performance' | 'machine_statistics' | 'confidence_analysis',
  filters: ReportFilters = {},
  granularity?: 'hourly' | 'daily' | 'weekly'
): Promise<Blob> {
  const queryParams = buildQueryParams(filters, { 
    report_type: reportType,
    ...(granularity && { granularity })
  });
  
  return authFetchBlob(`/api/v1/reports/export/json?${queryParams}`);
}

// Utility function to download blob as file
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}