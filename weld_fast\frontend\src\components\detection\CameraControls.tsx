// src/components/detection/CameraControls.tsx
import { Camera, CameraOff, RefreshCw, Loader2 } from 'lucide-react';

interface CameraControlsProps {
  onCapture: () => void | Promise<void>;
  onToggleCamera: () => void;
  cameraActive: boolean;
  isCapturing?: boolean;
}

export default function CameraControls({ onCapture, onToggleCamera, cameraActive, isCapturing = false }: CameraControlsProps) {
  return (
    <div className="p-4 bg-gray-800 flex items-center justify-between">
      {/* Switch Camera Button */}
      <button 
        onClick={onToggleCamera}
        className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors"
      >
        {cameraActive ? <CameraOff size={20} /> : <Camera size={20} />}
      </button>
      
      {/* Capture Button */}
      <button
        onClick={onCapture}
        disabled={!cameraActive || isCapturing}
        className={`p-6 rounded-full ${
          cameraActive && !isCapturing
            ? 'bg-red-600 hover:bg-red-700' 
            : 'bg-gray-600 cursor-not-allowed'
        } transition-colors flex items-center justify-center`}
      >
        {isCapturing ? (
          <Loader2 size={24} className="animate-spin text-white" />
        ) : (
          <span className="sr-only">Capture</span>
        )}
      </button>
      
      {/* Camera Switch Button (front/back) */}
      <button 
        className="p-2 rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors"
      >
        <RefreshCw size={20} />
      </button>
    </div>
  );
}