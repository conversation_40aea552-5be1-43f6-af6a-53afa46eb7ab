#!/usr/bin/env python3
"""
Simple test to verify sync endpoints are working
"""
import asyncio
import aiohttp
import json

API_BASE = "http://localhost:8000"

async def test_sync_health():
    """Test sync health endpoint"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{API_BASE}/api/v1/sync/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Sync health endpoint working:", data)
                    return True
                else:
                    print(f"❌ Sync health endpoint failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Sync health endpoint error: {e}")
        return False

async def test_api_health():
    """Test main API health endpoint"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{API_BASE}/api/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ API health endpoint working:", data)
                    return True
                else:
                    print(f"❌ API health endpoint failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ API health endpoint error: {e}")
        return False

async def test_frame_sync_unauthorized():
    """Test frame sync endpoint without auth (should fail)"""
    try:
        frame_data = {
            "operation_type": "create",
            "object_type": "frame",
            "object_id": "test-frame-123",
            "frame_data": {
                "frameId": "test-frame-123",
                "modelNumber": "TEST-001",
                "machineSerialNumber": "MACHINE-001",
                "inspectorName": "Test Inspector",
                "creationTimestamp": 1640995200000,
                "lastModifiedTimestamp": 1640995200000,
                "status": "active",
                "captureCount": 0
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{API_BASE}/api/v1/sync/frame",
                json=frame_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status in [401, 403]:  # Both are valid for unauthenticated
                    print("✅ Frame sync correctly requires authentication")
                    return True
                else:
                    text = await response.text()
                    print(f"❌ Expected 401/403 but got {response.status}: {text}")
                    return False
    except Exception as e:
        print(f"❌ Frame sync test error: {e}")
        return False

async def main():
    print("🧪 Testing Sync Implementation...")
    print("=" * 50)
    
    # Test basic health endpoints
    api_health = await test_api_health()
    sync_health = await test_sync_health()
    
    # Test sync endpoints (should require auth)
    frame_sync_auth = await test_frame_sync_unauthorized()
    
    print("\n📊 Test Results:")
    print("=" * 50)
    print(f"API Health: {'✅ PASS' if api_health else '❌ FAIL'}")
    print(f"Sync Health: {'✅ PASS' if sync_health else '❌ FAIL'}")
    print(f"Frame Sync Auth: {'✅ PASS' if frame_sync_auth else '❌ FAIL'}")
    
    all_passed = all([api_health, sync_health, frame_sync_auth])
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✨ Sync implementation is working correctly!")
        print("📝 Next steps:")
        print("   1. Start frontend with 'npm run dev'")
        print("   2. Login with admin/admin123 or inspector1/password123")
        print("   3. Create a frame and captures")
        print("   4. Use the manual sync button to sync to server")
    else:
        print("\n🔧 Please fix the failed tests before proceeding")

if __name__ == "__main__":
    asyncio.run(main())