// src/lib/detection/renderUtils.ts
import { DetectionResult } from '@/lib/db/types';
import { getClassColor } from './labels';

export interface RenderOptions {
  lineWidth?: number;
  fontSize?: number;
  confidenceThreshold?: number;
  showConfidence?: boolean;
  showLabels?: boolean;
  scaleFactor?: { x: number; y: number };
}

/**
 * Render detection results on canvas
 */
export function renderDetections(
  canvas: HTMLCanvasElement,
  detections: DetectionResult[],
  options: RenderOptions = {}
): void {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Could not get canvas context for rendering');
  }

  const {
    lineWidth = 3,
    fontSize = 16,
    confidenceThreshold = 0.25,
    showConfidence = true,
    showLabels = true,
    scaleFactor = { x: 1, y: 1 }
  } = options;

  // Filter detections by confidence threshold
  const filteredDetections = detections.filter(
    detection => detection.confidence >= confidenceThreshold
  );

  filteredDetections.forEach((detection, index) => {
    const { bbox, class: className, confidence } = detection;
    
    // Scale coordinates
    const x1 = bbox.x1 * scaleFactor.x;
    const y1 = bbox.y1 * scaleFactor.y;
    const x2 = bbox.x2 * scaleFactor.x;
    const y2 = bbox.y2 * scaleFactor.y;
    
    const width = x2 - x1;
    const height = y2 - y1;

    // Get color for this detection
    const color = getClassColor(index);
    
    // Draw bounding box
    ctx.strokeStyle = color;
    ctx.lineWidth = lineWidth;
    ctx.strokeRect(x1, y1, width, height);
    
    // Draw semi-transparent background for box
    ctx.fillStyle = color + '20'; // Add transparency
    ctx.fillRect(x1, y1, width, height);

    if (showLabels || showConfidence) {
      // Prepare label text
      let labelText = '';
      if (showLabels) {
        labelText = className;
      }
      if (showConfidence) {
        const confidenceText = `${Math.round(confidence * 100)}%`;
        labelText = showLabels ? `${labelText} (${confidenceText})` : confidenceText;
      }

      // Set font
      ctx.font = `${fontSize}px Arial`;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      // Measure text
      const textMetrics = ctx.measureText(labelText);
      const textWidth = textMetrics.width;
      const textHeight = fontSize;

      // Calculate label position
      const labelX = x1;
      const labelY = y1 > textHeight + 4 ? y1 - textHeight - 4 : y1 + height + 4;

      // Draw label background
      ctx.fillStyle = color;
      ctx.fillRect(labelX - 2, labelY - 2, textWidth + 4, textHeight + 4);

      // Draw label text
      ctx.fillStyle = '#FFFFFF';
      ctx.fillText(labelText, labelX, labelY);
    }
  });
}

/**
 * Clear canvas
 */
export function clearCanvas(canvas: HTMLCanvasElement): void {
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  }
}

/**
 * Copy image from one canvas to another
 */
export function copyCanvas(source: HTMLCanvasElement, target: HTMLCanvasElement): void {
  target.width = source.width;
  target.height = source.height;
  
  const ctx = target.getContext('2d');
  if (ctx) {
    ctx.drawImage(source, 0, 0);
  }
}

/**
 * Create a new canvas with detection overlay
 */
export function createDetectionCanvas(
  originalCanvas: HTMLCanvasElement,
  detections: DetectionResult[],
  options: RenderOptions = {}
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  copyCanvas(originalCanvas, canvas);
  renderDetections(canvas, detections, options);
  return canvas;
}