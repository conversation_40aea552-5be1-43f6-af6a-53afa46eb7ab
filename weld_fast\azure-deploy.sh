#!/bin/bash

# Azure Deployment Script for Weld Defect Detection System
# Make sure you're logged in: az login

set -e  # Exit on any error

# Configuration Variables
RESOURCE_GROUP="weld-detection-rg"
LOCATION="eastus"
ACR_NAME="welddetectionacr"
DB_NAME="weld-detection-db"
DB_ADMIN="weldadmin"
BACKEND_APP="weld-backend"
FRONTEND_APP="weld-frontend"
CONTAINER_ENV="weld-env"

echo "🚀 Starting Azure deployment for Weld Defect Detection System"

# Function to check if resource exists
resource_exists() {
    az "$1" show --name "$2" --resource-group "$RESOURCE_GROUP" &>/dev/null
}

# Step 1: Create Resource Group
echo "📦 Creating resource group..."
az group create --name $RESOURCE_GROUP --location $LOCATION

# Step 2: Create Container Registry
echo "🏗️ Creating Container Registry..."
if ! resource_exists "acr" $ACR_NAME; then
    az acr create --resource-group $RESOURCE_GROUP \
        --name $ACR_NAME --sku Basic --admin-enabled true
    echo "✅ Container Registry created"
else
    echo "ℹ️ Container Registry already exists"
fi

# Step 3: Create PostgreSQL Database
echo "🗄️ Creating PostgreSQL database..."
if ! az postgres flexible-server show --name $DB_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "⚠️ Please set database password:"
    read -s DB_PASSWORD
    
    az postgres flexible-server create \
        --resource-group $RESOURCE_GROUP \
        --name $DB_NAME \
        --admin-user $DB_ADMIN \
        --admin-password "$DB_PASSWORD" \
        --sku-name Standard_B1ms \
        --storage-size 32 \
        --version 14 \
        --location $LOCATION
    
    # Configure firewall
    az postgres flexible-server firewall-rule create \
        --resource-group $RESOURCE_GROUP \
        --name $DB_NAME \
        --rule-name AllowAzureServices \
        --start-ip-address 0.0.0.0 \
        --end-ip-address 0.0.0.0
    
    echo "✅ Database created"
else
    echo "ℹ️ Database already exists"
fi

# Step 4: Create Container Apps Environment
echo "🌐 Creating Container Apps environment..."
if ! az containerapp env show --name $CONTAINER_ENV --resource-group $RESOURCE_GROUP &>/dev/null; then
    az containerapp env create \
        --name $CONTAINER_ENV \
        --resource-group $RESOURCE_GROUP \
        --location $LOCATION
    echo "✅ Container Apps environment created"
else
    echo "ℹ️ Container Apps environment already exists"
fi

# Step 5: Build and Push Backend Container
echo "🐳 Building and pushing backend container..."
cd backend
az acr build --registry $ACR_NAME --image backend:latest .
cd ..

# Step 6: Deploy Backend Container App
echo "🚀 Deploying backend container app..."
ACR_SERVER=$(az acr show --name $ACR_NAME --resource-group $RESOURCE_GROUP --query loginServer --output tsv)

# Get database connection string
DB_HOST=$(az postgres flexible-server show --name $DB_NAME --resource-group $RESOURCE_GROUP --query fullyQualifiedDomainName --output tsv)
DATABASE_URL="*************************************************/postgres?sslmode=require"

if ! az containerapp show --name $BACKEND_APP --resource-group $RESOURCE_GROUP &>/dev/null; then
    az containerapp create \
        --name $BACKEND_APP \
        --resource-group $RESOURCE_GROUP \
        --environment $CONTAINER_ENV \
        --image $ACR_SERVER/backend:latest \
        --registry-server $ACR_SERVER \
        --registry-username $ACR_NAME \
        --registry-password $(az acr credential show --name $ACR_NAME --query passwords[0].value --output tsv) \
        --ingress external \
        --target-port 8000 \
        --min-replicas 1 \
        --max-replicas 3 \
        --env-vars \
            DATABASE_URL="$DATABASE_URL" \
            SECRET_KEY="$(openssl rand -base64 32)" \
            ALLOWED_ORIGINS="*"
    echo "✅ Backend deployed"
else
    echo "ℹ️ Backend already exists, updating..."
    az containerapp update \
        --name $BACKEND_APP \
        --resource-group $RESOURCE_GROUP \
        --image $ACR_SERVER/backend:latest
fi

# Get backend URL
BACKEND_URL=$(az containerapp show --name $BACKEND_APP --resource-group $RESOURCE_GROUP --query properties.configuration.ingress.fqdn --output tsv)
echo "🔗 Backend URL: https://$BACKEND_URL"

# Step 7: Update Frontend Environment
echo "🎨 Updating frontend environment..."
sed -i "s|NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=https://$BACKEND_URL|" frontend/.env.production

# Step 8: Build Frontend
echo "🏗️ Building frontend..."
cd frontend
npm install
npm run build
cd ..

# Step 9: Deploy Frontend Static Web App
echo "🌐 Deploying frontend static web app..."
if ! az staticwebapp show --name $FRONTEND_APP --resource-group $RESOURCE_GROUP &>/dev/null; then
    az staticwebapp create \
        --name $FRONTEND_APP \
        --resource-group $RESOURCE_GROUP \
        --source . \
        --location $LOCATION \
        --app-location "frontend" \
        --output-location "out"
    echo "✅ Frontend deployed"
else
    echo "ℹ️ Frontend already exists"
fi

# Get frontend URL
FRONTEND_URL=$(az staticwebapp show --name $FRONTEND_APP --resource-group $RESOURCE_GROUP --query defaultHostname --output tsv)

# Step 10: Update CORS settings
echo "🔐 Updating CORS settings..."
az containerapp update \
    --name $BACKEND_APP \
    --resource-group $RESOURCE_GROUP \
    --set-env-vars ALLOWED_ORIGINS="[\"https://$FRONTEND_URL\"]"

echo ""
echo "🎉 Deployment completed successfully!"
echo "📱 Frontend URL: https://$FRONTEND_URL"
echo "🔧 Backend URL: https://$BACKEND_URL"
echo ""
echo "📝 Next steps:"
echo "1. Test the application at the frontend URL"
echo "2. Configure custom domain (optional)"
echo "3. Set up monitoring and alerts"
echo "4. Configure CI/CD pipeline"