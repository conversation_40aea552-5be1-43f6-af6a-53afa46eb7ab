from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional, Dict, Any, List
import time
import uuid
import json

from ..models.database import Frame, Capture, SyncStatus as DbSyncStatus
from ..schemas.sync import (
    SyncFrameRequest, SyncCaptureRequest, SyncResponse, 
    SyncOperationType, SyncObjectType, SyncStatus
)
from ..schemas.frame import FrameCreate
from .frame_service import FrameService

class SyncService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.frame_service = FrameService(db)

    async def sync_frame_from_client(
        self, 
        sync_request: SyncFrameRequest, 
        client_id: Optional[str] = None
    ) -> SyncResponse:
        """
        Sync a frame from client to server.
        Handles create/update operations with duplicate detection.
        """
        try:
            frame_data = sync_request.frame_data
            frame_id = sync_request.object_id
            
            # Check if frame already exists on server
            existing_frame = await self._check_duplicate_frame(frame_id, client_id)
            
            if sync_request.operation_type == SyncOperationType.CREATE:
                if existing_frame:
                    return SyncResponse(
                        success=True,
                        message="Frame already exists on server",
                        object_id=frame_id,
                        object_type=SyncObjectType.FRAME,
                        server_object_id=existing_frame.frame_id
                    )
                
                # Create new frame
                new_frame = await self._create_frame_from_sync(frame_data, frame_id)
                return SyncResponse(
                    success=True,
                    message="Frame created successfully",
                    object_id=frame_id,
                    object_type=SyncObjectType.FRAME,
                    server_object_id=new_frame.frame_id
                )
                
            elif sync_request.operation_type == SyncOperationType.UPDATE:
                if not existing_frame:
                    # Frame doesn't exist, create it instead
                    new_frame = await self._create_frame_from_sync(frame_data, frame_id)
                    return SyncResponse(
                        success=True,
                        message="Frame created (was missing on server)",
                        object_id=frame_id,
                        object_type=SyncObjectType.FRAME,
                        server_object_id=new_frame.frame_id
                    )
                
                # Update existing frame
                updated_frame = await self._update_frame_from_sync(existing_frame, frame_data)
                return SyncResponse(
                    success=True,
                    message="Frame updated successfully",
                    object_id=frame_id,
                    object_type=SyncObjectType.FRAME,
                    server_object_id=updated_frame.frame_id
                )
            
            else:  # DELETE
                if existing_frame:
                    await self.frame_service.delete_frame(frame_id)
                    return SyncResponse(
                        success=True,
                        message="Frame deleted successfully",
                        object_id=frame_id,
                        object_type=SyncObjectType.FRAME
                    )
                else:
                    return SyncResponse(
                        success=True,
                        message="Frame already deleted or doesn't exist",
                        object_id=frame_id,
                        object_type=SyncObjectType.FRAME
                    )
        
        except Exception as e:
            return SyncResponse(
                success=False,
                message=f"Frame sync failed: {str(e)}",
                object_id=frame_id,
                object_type=SyncObjectType.FRAME
            )

    async def sync_capture_from_client(
        self, 
        sync_request: SyncCaptureRequest, 
        client_id: Optional[str] = None
    ) -> SyncResponse:
        """
        Sync a capture from client to server.
        Handles create/update operations with duplicate detection.
        """
        try:
            capture_data = sync_request.capture_data
            capture_id = sync_request.object_id
            frame_id = sync_request.frame_id
            
            # Check if capture already exists on server
            existing_capture = await self._check_duplicate_capture(capture_id, client_id)
            
            # Ensure parent frame exists
            parent_frame = await self._check_duplicate_frame(frame_id, client_id)
            if not parent_frame:
                return SyncResponse(
                    success=False,
                    message=f"Parent frame {frame_id} not found on server",
                    object_id=capture_id,
                    object_type=SyncObjectType.CAPTURE
                )
            
            if sync_request.operation_type == SyncOperationType.CREATE:
                if existing_capture:
                    return SyncResponse(
                        success=True,
                        message="Capture already exists on server",
                        object_id=capture_id,
                        object_type=SyncObjectType.CAPTURE,
                        server_object_id=existing_capture.capture_id
                    )
                
                # Create new capture
                new_capture = await self._create_capture_from_sync(capture_data, capture_id, frame_id)
                return SyncResponse(
                    success=True,
                    message="Capture created successfully",
                    object_id=capture_id,
                    object_type=SyncObjectType.CAPTURE,
                    server_object_id=new_capture.capture_id
                )
                
            elif sync_request.operation_type == SyncOperationType.UPDATE:
                if not existing_capture:
                    # Capture doesn't exist, create it instead
                    new_capture = await self._create_capture_from_sync(capture_data, capture_id, frame_id)
                    return SyncResponse(
                        success=True,
                        message="Capture created (was missing on server)",
                        object_id=capture_id,
                        object_type=SyncObjectType.CAPTURE,
                        server_object_id=new_capture.capture_id
                    )
                
                # Update existing capture
                updated_capture = await self._update_capture_from_sync(existing_capture, capture_data)
                return SyncResponse(
                    success=True,
                    message="Capture updated successfully",
                    object_id=capture_id,
                    object_type=SyncObjectType.CAPTURE,
                    server_object_id=updated_capture.capture_id
                )
            
            else:  # DELETE
                if existing_capture:
                    await self.db.delete(existing_capture)
                    await self.db.commit()
                    return SyncResponse(
                        success=True,
                        message="Capture deleted successfully",
                        object_id=capture_id,
                        object_type=SyncObjectType.CAPTURE
                    )
                else:
                    return SyncResponse(
                        success=True,
                        message="Capture already deleted or doesn't exist",
                        object_id=capture_id,
                        object_type=SyncObjectType.CAPTURE
                    )
        
        except Exception as e:
            return SyncResponse(
                success=False,
                message=f"Capture sync failed: {str(e)}",
                object_id=capture_id,
                object_type=SyncObjectType.CAPTURE
            )

    async def _check_duplicate_frame(self, frame_id: str, client_id: Optional[str] = None) -> Optional[Frame]:
        """Check if frame already exists on server."""
        result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        return result.scalar_one_or_none()

    async def _check_duplicate_capture(self, capture_id: str, client_id: Optional[str] = None) -> Optional[Capture]:
        """Check if capture already exists on server."""
        result = await self.db.execute(
            select(Capture).where(Capture.capture_id == capture_id)
        )
        return result.scalar_one_or_none()

    async def _create_frame_from_sync(self, frame_data: Dict[str, Any], frame_id: str) -> Frame:
        """Create a new frame from sync data."""
        current_time = int(time.time() * 1000)
        
        # Get inspector_id, fallback to admin user if not provided
        inspector_id = frame_data.get('inspector_id') or frame_data.get('inspectorId')
        if not inspector_id:
            # Fallback: try to find user by inspector_name, or use admin
            inspector_name = frame_data.get('inspector_name') or frame_data.get('inspectorName', '')
            from sqlalchemy import select
            from ..models.database import User
            
            # Try to find user by username or full_name
            result = await self.db.execute(
                select(User).where(
                    (User.username == inspector_name) | (User.full_name == inspector_name)
                )
            )
            user = result.scalar_one_or_none()
            
            if user:
                inspector_id = user.user_id
            else:
                # Fallback to admin user
                result = await self.db.execute(
                    select(User).where(User.role == 'admin')
                )
                admin_user = result.scalar_one_or_none()
                inspector_id = admin_user.user_id if admin_user else None
        
        # Map client data to server frame model - handle both camelCase and snake_case
        db_frame = Frame(
            frame_id=frame_id,  # Use client-provided ID
            model_number=frame_data.get('model_number') or frame_data.get('modelNumber', ''),
            machine_serial_number=frame_data.get('machine_serial_number') or frame_data.get('machineSerialNumber', ''),
            inspector_name=frame_data.get('inspector_name') or frame_data.get('inspectorName', ''),
            inspector_id=inspector_id,  # Required field, with fallback logic
            creation_timestamp=frame_data.get('creation_timestamp') or frame_data.get('creationTimestamp', current_time),
            last_modified_timestamp=frame_data.get('last_modified_timestamp') or frame_data.get('lastModifiedTimestamp', current_time),
            status=frame_data.get('status', 'active'),
            capture_count=frame_data.get('capture_count') or frame_data.get('captureCount', 0),
            frame_metadata=frame_data.get('frame_metadata') or frame_data.get('metadata'),
            sync_status=DbSyncStatus.SYNCED,
            last_synced_at=current_time
        )
        
        self.db.add(db_frame)
        await self.db.commit()
        await self.db.refresh(db_frame)
        
        return db_frame

    async def _update_frame_from_sync(self, existing_frame: Frame, frame_data: Dict[str, Any]) -> Frame:
        """Update existing frame with sync data."""
        current_time = int(time.time() * 1000)
        
        # Update fields from client data - handle both camelCase and snake_case
        existing_frame.model_number = frame_data.get('model_number') or frame_data.get('modelNumber', existing_frame.model_number)
        existing_frame.machine_serial_number = frame_data.get('machine_serial_number') or frame_data.get('machineSerialNumber', existing_frame.machine_serial_number)
        existing_frame.inspector_name = frame_data.get('inspector_name') or frame_data.get('inspectorName', existing_frame.inspector_name)
        existing_frame.last_modified_timestamp = frame_data.get('last_modified_timestamp') or frame_data.get('lastModifiedTimestamp', current_time)
        existing_frame.status = frame_data.get('status', existing_frame.status)
        existing_frame.capture_count = frame_data.get('capture_count') or frame_data.get('captureCount', existing_frame.capture_count)
        existing_frame.frame_metadata = frame_data.get('frame_metadata') or frame_data.get('metadata', existing_frame.frame_metadata)
        existing_frame.sync_status = DbSyncStatus.SYNCED
        existing_frame.last_synced_at = current_time
        
        await self.db.commit()
        await self.db.refresh(existing_frame)
        
        return existing_frame

    async def _create_capture_from_sync(self, capture_data: Dict[str, Any], capture_id: str, frame_id: str) -> Capture:
        """Create a new capture from sync data."""
        current_time = int(time.time() * 1000)
        
        # Handle image blobs - from multipart upload or base64
        original_image_blob = None
        processed_image_blob = None
        thumbnail_blob = None
        
        # Check for image blobs (from multipart upload or base64)
        if 'originalImageBlob' in capture_data:
            original_image_blob = capture_data['originalImageBlob']
        elif 'original_image_blob' in capture_data:
            original_image_blob = capture_data['original_image_blob']
        
        if 'processedImageBlob' in capture_data:
            processed_image_blob = capture_data['processedImageBlob']
        elif 'processed_image_blob' in capture_data:
            processed_image_blob = capture_data['processed_image_blob']
            
        if 'thumbnailBlob' in capture_data:
            thumbnail_blob = capture_data['thumbnailBlob']
        elif 'thumbnail_blob' in capture_data:
            thumbnail_blob = capture_data['thumbnail_blob']
        
        db_capture = Capture(
            capture_id=capture_id,  # Use client-provided ID
            frame_id=frame_id,
            capture_timestamp=capture_data.get('capture_timestamp') or capture_data.get('captureTimestamp', current_time),
            original_image_blob=original_image_blob,
            processed_image_blob=processed_image_blob,
            thumbnail_blob=thumbnail_blob,
            detection_results=capture_data.get('detection_results') or capture_data.get('detectionResults', []),
            sync_status=DbSyncStatus.SYNCED,
            sync_version=1,
            last_sync_attempt=current_time
        )
        
        self.db.add(db_capture)
        await self.db.commit()
        await self.db.refresh(db_capture)
        
        return db_capture

    async def _update_capture_from_sync(self, existing_capture: Capture, capture_data: Dict[str, Any]) -> Capture:
        """Update existing capture with sync data."""
        current_time = int(time.time() * 1000)
        
        # Update fields from client data - handle both camelCase and snake_case
        existing_capture.capture_timestamp = capture_data.get('capture_timestamp') or capture_data.get('captureTimestamp', existing_capture.capture_timestamp)
        existing_capture.detection_results = capture_data.get('detection_results') or capture_data.get('detectionResults', existing_capture.detection_results)
        existing_capture.sync_status = DbSyncStatus.SYNCED
        existing_capture.sync_version += 1
        existing_capture.last_sync_attempt = current_time
        
        # Update image blobs if provided (handle both naming conventions)
        if 'originalImageBlob' in capture_data:
            existing_capture.original_image_blob = capture_data['originalImageBlob']
        elif 'original_image_blob' in capture_data:
            existing_capture.original_image_blob = capture_data['original_image_blob']
            
        if 'processedImageBlob' in capture_data:
            existing_capture.processed_image_blob = capture_data['processedImageBlob']
        elif 'processed_image_blob' in capture_data:
            existing_capture.processed_image_blob = capture_data['processed_image_blob']
            
        if 'thumbnailBlob' in capture_data:
            existing_capture.thumbnail_blob = capture_data['thumbnailBlob']
        elif 'thumbnail_blob' in capture_data:
            existing_capture.thumbnail_blob = capture_data['thumbnail_blob']
        
        await self.db.commit()
        await self.db.refresh(existing_capture)
        
        return existing_capture