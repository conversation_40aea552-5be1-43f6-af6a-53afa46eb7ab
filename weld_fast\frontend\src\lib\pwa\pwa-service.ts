'use client';

import { reactiveSyncService } from '@/lib/sync/reactiveSyncService';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
}

class PWAService {
  private registration: ServiceWorkerRegistration | null = null;
  private isInstallPromptAvailable = false;
  private deferredPrompt: BeforeInstallPromptEvent | null = null;

  async initialize() {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return;
    }

    // Skip service worker registration in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('PWA service worker disabled in development mode');
      // Still initialize other PWA features like install prompt
      this.initializeInstallPrompt();
      return;
    }

    try {
      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js');
      
      // Listen for updates
      this.registration.addEventListener('updatefound', () => {
        console.log('Service worker update found');
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage.bind(this));

      // Initialize install prompt
      this.initializeInstallPrompt();

      // Register for background sync
      if (this.registration.sync) {
        this.registration.sync.register('weld-sync');
      }

      console.log('PWA service initialized');
    } catch (error) {
      console.error('Failed to register service worker:', error);
    }
  }

  private initializeInstallPrompt() {
    // Listen for install prompt
    window.addEventListener('beforeinstallprompt', this.handleInstallPrompt.bind(this));

    // Listen for app installed
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      this.deferredPrompt = null;
      this.isInstallPromptAvailable = false;
    });
  }

  private handleServiceWorkerMessage(event: MessageEvent) {
    if (event.data?.type === 'BACKGROUND_SYNC_TRIGGER') {
      // Trigger sync when service worker requests it
      reactiveSyncService.processSyncQueue();
    }
  }

  private handleInstallPrompt(event: Event) {
    // Prevent the mini-infobar from appearing on mobile
    event.preventDefault();
    // Stash the event so it can be triggered later
    this.deferredPrompt = event as BeforeInstallPromptEvent;
    this.isInstallPromptAvailable = true;
    
    // Dispatch custom event for UI components
    window.dispatchEvent(new CustomEvent('pwa-install-available'));
  }

  async showInstallPrompt(): Promise<boolean> {
    if (!this.isInstallPromptAvailable || !this.deferredPrompt) {
      return false;
    }

    try {
      // Show the install prompt
      await this.deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const result = await this.deferredPrompt.userChoice;
      
      // Reset the deferred prompt
      this.deferredPrompt = null;
      this.isInstallPromptAvailable = false;
      
      return result.outcome === 'accepted';
    } catch (error) {
      console.error('Error showing install prompt:', error);
      return false;
    }
  }

  isInstallAvailable(): boolean {
    return this.isInstallPromptAvailable;
  }

  isStandalone(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(display-mode: standalone)').matches ||
           Boolean((window.navigator as { standalone?: boolean }).standalone) ||
           document.referrer.includes('android-app://');
  }

  async requestPersistentStorage(): Promise<boolean> {
    if (typeof window === 'undefined') return false;
    
    if ('storage' in navigator && 'persist' in navigator.storage) {
      try {
        const granted = await navigator.storage.persist();
        console.log('Persistent storage:', granted ? 'granted' : 'denied');
        return granted;
      } catch (error) {
        console.error('Error requesting persistent storage:', error);
        return false;
      }
    }
    return false;
  }

  async getStorageEstimate() {
    if (typeof window === 'undefined') return null;
    
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        return await navigator.storage.estimate();
      } catch (error) {
        console.error('Error getting storage estimate:', error);
        return null;
      }
    }
    return null;
  }

  // Trigger background sync manually
  async triggerBackgroundSync() {
    if (this.registration?.sync) {
      try {
        await this.registration.sync.register('weld-sync');
      } catch (error) {
        console.error('Error registering background sync:', error);
      }
    } else {
      // In development mode, trigger sync directly
      console.log('Triggering sync directly (no service worker)');
      reactiveSyncService.processSyncQueue();
    }
  }
}

export const pwaService = new PWAService();