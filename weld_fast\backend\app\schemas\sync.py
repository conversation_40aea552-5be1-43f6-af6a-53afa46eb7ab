from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Literal
from enum import Enum

class SyncOperationType(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"

class SyncObjectType(str, Enum):
    FRAME = "frame"
    CAPTURE = "capture"

class SyncStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

# Base sync request
class SyncRequestBase(BaseModel):
    operation_type: SyncOperationType
    object_type: SyncObjectType
    object_id: str
    client_id: Optional[str] = None

# Frame sync request
class SyncFrameRequest(SyncRequestBase):
    object_type: Literal[SyncObjectType.FRAME] = Field(default=SyncObjectType.FRAME)
    frame_data: Dict[str, Any] = Field(..., description="Complete frame data from client")

# Capture sync request  
class SyncCaptureRequest(SyncRequestBase):
    object_type: Literal[SyncObjectType.CAPTURE] = Field(default=SyncObjectType.CAPTURE)
    capture_data: Dict[str, Any] = Field(..., description="Complete capture data from client")
    frame_id: str = Field(..., description="Parent frame ID")

# Sync response
class SyncResponse(BaseModel):
    success: bool
    message: str
    object_id: str
    object_type: SyncObjectType
    server_object_id: Optional[str] = None  # In case server assigns different ID
    conflicts: Optional[List[str]] = Field(default=None, description="List of conflicting fields")
    
class SyncErrorDetail(BaseModel):
    error_code: str
    error_message: str
    field: Optional[str] = None

class SyncBatchRequest(BaseModel):
    requests: List[SyncFrameRequest | SyncCaptureRequest] = Field(..., max_items=50)
    client_id: Optional[str] = None

class SyncBatchResponse(BaseModel):
    results: List[SyncResponse]
    total_requested: int
    successful: int
    failed: int
    errors: Optional[List[SyncErrorDetail]] = None

# Sync status check
class SyncStatusResponse(BaseModel):
    object_id: str
    object_type: SyncObjectType
    sync_status: SyncStatus
    last_synced_at: Optional[int] = None
    error_message: Optional[str] = None
    retry_count: int = 0