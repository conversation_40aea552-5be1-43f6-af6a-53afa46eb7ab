// src/lib/db/types.ts

// Frame Session
export interface Frame {
    frameId: string;
    modelNumber: string;
    machineSerialNumber: string;
    inspectorName: string;
    creationTimestamp: number;
    lastModifiedTimestamp: number;
    status: 'active' | 'completed' | 'archived';
    captureCount: number;
    metadata?: Record<string, unknown>;
    syncStatus: 'synced' | 'pending' | 'conflict';
    lastSyncedAt?: number;
  }
  
  // Capture (Detection Result)
  export interface Capture {
    captureId: string;
    frameId: string;
    captureTimestamp: number;
    originalImageBlob?: Blob;
    processedImageBlob?: Blob;
    thumbnailBlob?: Blob;
    detectionResults: DetectionResult[];
    syncStatus: 'synced' | 'pending' | 'conflict';
    syncVersion: number;
    lastSyncAttempt?: number;
  }
  
  // Detection Result (Object Found)
  export interface DetectionResult {
    id: string;
    class: string;
    confidence: number;
    bbox: {
      x1: number;
      y1: number;
      x2: number;
      y2: number;
    };
  }
  
  // Sync Queue Item
  export interface SyncQueueItem {
    queueId?: number; // Auto-incremented by IndexedDB
    operationType: 'create' | 'update' | 'delete';
    objectType: 'frame' | 'capture';
    objectId: string;
    priority: number;
    createdAt: number;
    attemptCount: number;
    lastAttempt?: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    context?: {
      frameId?: string;
      [key: string]: unknown;
    };
    errorDetails?: {
      message?: string;
      errorType?: 'network' | 'auth' | 'server' | 'client' | 'unknown';
      finalAttempt?: boolean;
      retryScheduled?: boolean;
      nextRetryAt?: number;
    };
  }