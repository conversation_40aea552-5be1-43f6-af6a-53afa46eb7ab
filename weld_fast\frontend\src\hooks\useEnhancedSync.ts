'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { enhancedSyncStateManager } from '@/lib/sync/enhancedSyncStateManager';
import { reactiveDatabase } from '@/lib/db/reactiveDatabase';
import type { EnhancedSyncState, FrameState, ItemUpdate, SyncProgress } from '@/lib/sync/types';
import type { DatabaseChange } from '@/lib/db/reactiveDatabase';

/**
 * Enhanced hook for reactive sync state management
 * 
 * Provides immediate updates when sync operations complete, eliminating the need for polling.
 */
export function useEnhancedSyncState(callback?: (state: EnhancedSyncState) => void) {
  const [syncState, setSyncState] = useState<EnhancedSyncState | null>(null);
  const callbackRef = useRef(callback);
  
  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    console.log('[useEnhancedSyncState] Setting up sync state subscription');
    
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((state) => {
      console.log('[useEnhancedSyncState] Sync state updated:', {
        isProcessing: state.isProcessing,
        stats: state.stats,
        frameStatesCount: state.frameStates.size,
        itemUpdatesCount: state.itemUpdates.length
      });
      
      setSyncState(state);
      
      if (callbackRef.current) {
        callbackRef.current(state);
      }
    });

    return () => {
      console.log('[useEnhancedSyncState] Cleaning up sync state subscription');
      subscription.unsubscribe();
    };
  }, []);

  return syncState;
}

/**
 * Hook for frame-specific sync state
 * 
 * Provides reactive updates for a specific frame's sync status.
 */
export function useFrameSyncState(frameId: string) {
  const [frameState, setFrameState] = useState<FrameState | null>(null);
  const [itemUpdates, setItemUpdates] = useState<ItemUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!frameId) return;

    console.log('[useFrameSyncState] Setting up frame sync state for:', frameId);
    setIsLoading(true);

    // Subscribe to frame-specific state
    const frameStateSubscription = enhancedSyncStateManager
      .getFrameState(frameId)
      .subscribe((state) => {
        console.log('[useFrameSyncState] Frame state updated:', { frameId, state });
        setFrameState(state);
        setIsLoading(false);
      });

    // Subscribe to frame-specific item updates
    const itemUpdatesSubscription = enhancedSyncStateManager
      .getFrameItemUpdates(frameId)
      .subscribe((updates) => {
        console.log('[useFrameSyncState] Frame item updates:', { frameId, updatesCount: updates.length });
        setItemUpdates(updates);
      });

    return () => {
      console.log('[useFrameSyncState] Cleaning up frame sync state for:', frameId);
      frameStateSubscription.unsubscribe();
      itemUpdatesSubscription.unsubscribe();
    };
  }, [frameId]);

  return {
    frameState,
    itemUpdates,
    isLoading
  };
}

/**
 * Hook for database change notifications
 * 
 * Provides reactive updates when database items change.
 */
export function useDatabaseChanges(frameId?: string) {
  const [changes, setChanges] = useState<DatabaseChange[]>([]);
  const [lastChange, setLastChange] = useState<DatabaseChange | null>(null);

  useEffect(() => {
    console.log('[useDatabaseChanges] Setting up database change subscription for frame:', frameId);

    const subscription = frameId
      ? reactiveDatabase.getFrameChanges(frameId)
      : reactiveDatabase.getChanges();

    const changeSubscription = subscription.subscribe((dbChanges) => {
      console.log('[useDatabaseChanges] Database changes received:', {
        frameId,
        changesCount: dbChanges.length,
        latestChange: dbChanges[dbChanges.length - 1],
        allChanges: dbChanges.map(c => ({ type: c.type, id: c.id, frameId: c.frameId, operation: c.operation, changes: c.changes }))
      });
      
      setChanges(dbChanges);
      
      if (dbChanges.length > 0) {
        const latestChange = dbChanges[dbChanges.length - 1];
        console.log('[useDatabaseChanges] Setting latest change:', {
          frameId,
          latestChange: { type: latestChange.type, id: latestChange.id, frameId: latestChange.frameId, operation: latestChange.operation }
        });
        setLastChange(latestChange);
      }
    });

    return () => {
      console.log('[useDatabaseChanges] Cleaning up database change subscription');
      changeSubscription.unsubscribe();
    };
  }, [frameId]);

  return {
    changes,
    lastChange
  };
}

// useSyncCompletion hook removed - no longer needed with reactive system

/**
 * Hook for reactive capture loading
 * 
 * Automatically reloads captures when database changes occur for a frame.
 */
export function useReactiveCaptures(
  frameId: string,
  loadCaptures: () => Promise<void>
) {
  const loadCapturesRef = useRef(loadCaptures);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const isLoadingRef = useRef(false);

  // Update callback ref when it changes (no dependencies to prevent infinite loops)
  useEffect(() => {
    loadCapturesRef.current = loadCaptures;
  }, [loadCaptures]);

  // Initial load on mount
  useEffect(() => {
    if (!hasInitialLoad && !isLoadingRef.current) {
      isLoadingRef.current = true;
      setIsLoading(true);
      setHasInitialLoad(true);

      loadCapturesRef.current().finally(() => {
        isLoadingRef.current = false;
        setIsLoading(false);
      });
    }
  }, [frameId, hasInitialLoad]);

  // Subscribe to database changes for this frame
  const { lastChange } = useDatabaseChanges(frameId);

  // Reload captures when database changes occur (but not on initial load)
  useEffect(() => {
    console.log('[useReactiveCaptures] Database change effect triggered:', {
      lastChange: lastChange ? { type: lastChange.type, id: lastChange.id, frameId: lastChange.frameId, operation: lastChange.operation } : null,
      currentFrameId: frameId,
      hasInitialLoad,
      isLoading: isLoadingRef.current
    });

    if (!lastChange || lastChange.frameId !== frameId || !hasInitialLoad || isLoadingRef.current) {
      console.log('[useReactiveCaptures] Skipping reload due to conditions:', {
        noLastChange: !lastChange,
        frameIdMismatch: lastChange ? lastChange.frameId !== frameId : false,
        noInitialLoad: !hasInitialLoad,
        isLoading: isLoadingRef.current
      });
      return;
    }

    console.log('[useReactiveCaptures] Triggering capture reload due to database change');
    isLoadingRef.current = true;
    setIsLoading(true);

    // Small delay to ensure database transaction is committed
    setTimeout(async () => {
      try {
        console.log('[useReactiveCaptures] Executing capture reload');
        await loadCapturesRef.current();
        console.log('[useReactiveCaptures] Capture reload completed successfully');
      } catch (error) {
        console.error('[useReactiveCaptures] Error reloading captures:', error);
      } finally {
        isLoadingRef.current = false;
        setIsLoading(false);
        console.log('[useReactiveCaptures] Capture reload finished');
      }
    }, 100);
  }, [lastChange, frameId, hasInitialLoad]);

  return {
    isLoading
  };
}

/**
 * Hook for reactive frame sync progress
 *
 * Provides real-time progress updates for frame-specific sync operations.
 */
export function useFrameSyncProgress(frameId: string) {
  const [progress, setProgress] = useState<SyncProgress | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEnhancedSyncState(useCallback((state: EnhancedSyncState) => {
    // Check if current sync operation is for this frame
    const isFrameSync = state.progress?.currentItem?.includes(frameId);

    if (isFrameSync && state.progress?.isProcessing) {
      setProgress(state.progress);
      setIsProcessing(true);
    } else if (!state.isProcessing) {
      // Sync completed or stopped
      setProgress(null);
      setIsProcessing(false);
    }
  }, [frameId]));

  return {
    progress,
    isProcessing
  };
}

/**
 * Hook for reactive sync operations
 *
 * Provides methods to trigger sync operations through the reactive system.
 */
export function useReactiveSyncOperations() {
  const [isOperating, setIsOperating] = useState(false);

  const triggerFrameSync = useCallback(async (frameId: string) => {
    if (isOperating) {
      console.warn('[useReactiveSyncOperations] Sync operation already in progress');
      return;
    }

    setIsOperating(true);
    try {
      console.log('[useReactiveSyncOperations] Triggering reactive frame sync for:', frameId);

      // Import reactive sync service
      const { reactiveSyncService } = await import('@/lib/sync/reactiveSyncService');

      // Trigger the sync operation - progress updates handled automatically by reactive service
      await reactiveSyncService.processSyncQueueForFrame(frameId);

      console.log('[useReactiveSyncOperations] Frame sync completed for:', frameId);
    } catch (error) {
      console.error('[useReactiveSyncOperations] Frame sync failed:', error);
      throw error;
    } finally {
      setIsOperating(false);
    }
  }, [isOperating]);

  const triggerGlobalSync = useCallback(async () => {
    if (isOperating) {
      console.warn('[useReactiveSyncOperations] Sync operation already in progress');
      return;
    }

    setIsOperating(true);
    try {
      console.log('[useReactiveSyncOperations] Triggering reactive global sync');

      // Import reactive sync service
      const { reactiveSyncService } = await import('@/lib/sync/reactiveSyncService');

      // Trigger the global sync operation - progress updates handled automatically by reactive service
      await reactiveSyncService.processSyncQueue();

      console.log('[useReactiveSyncOperations] Global sync completed');
    } catch (error) {
      console.error('[useReactiveSyncOperations] Global sync failed:', error);
      throw error;
    } finally {
      setIsOperating(false);
    }
  }, [isOperating]);

  const stopSync = useCallback(async () => {
    try {
      console.log('[useReactiveSyncOperations] Stopping sync operation');

      // Import reactive sync service
      const { reactiveSyncService } = await import('@/lib/sync/reactiveSyncService');

      // Stop the sync operation
      reactiveSyncService.stopSync();

      console.log('[useReactiveSyncOperations] Sync operation stopped');
    } catch (error) {
      console.error('[useReactiveSyncOperations] Failed to stop sync:', error);
      throw error;
    }
  }, []);

  return {
    triggerFrameSync,
    triggerGlobalSync,
    stopSync,
    isOperating
  };
}

/**
 * Legacy compatibility hook
 *
 * Provides backward compatibility with existing useSyncState usage.
 */
export function useSyncState(callback: (state: { isProcessing: boolean; stats: unknown; progress: unknown }) => void) {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEnhancedSyncState(useCallback((state: EnhancedSyncState) => {
    // Convert enhanced state to legacy format for compatibility
    const legacyState = {
      isProcessing: state.isProcessing,
      stats: state.stats,
      progress: state.progress
    };

    callbackRef.current(legacyState);
  }, []));
}
