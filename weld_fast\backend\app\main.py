from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .dependencies import get_query_token, get_token_header
from .internal import admin
from .routers import items, users, frames, auth, captures, sync, reports
from .database import create_tables, get_db
from .auth import create_default_users
from .config import settings


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Create database tables on startup
    await create_tables()
    
    # Create default users
    async for db in get_db():
        await create_default_users(db)
        break
    
    yield


app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(frames.router)
app.include_router(captures.router)
app.include_router(sync.router)
app.include_router(reports.router)
app.include_router(users.router)
app.include_router(items.router)
app.include_router(
    admin.router,
    prefix="/admin",
    tags=["admin"],
    dependencies=[Depends(get_token_header)],
    responses={418: {"description": "I'm a teapot"}},
)

@app.get("/")
async def root():
    return {"message": "Weld Defect Detection API - Backend Running"}

@app.get("/api/health")
async def health_check():
    """Health check endpoint for frontend connectivity testing"""
    return {
        "status": "healthy",
        "message": "Weld Defect Detection API is running",
        "version": "1.0.0"
    }
