'use client';

import { useState, useEffect, useCallback } from 'react';
import { refreshOrchestrator } from '@/lib/refresh/RefreshOrchestrator';
import { RefreshStats, RefreshError } from '@/lib/refresh/types';
import { Activity, AlertTriangle, CheckCircle, Clock, X } from 'lucide-react';

interface RefreshMonitorProps {
  className?: string;
  showDetails?: boolean;
}

export default function RefreshMonitor({ 
  className = '', 
  showDetails = false 
}: RefreshMonitorProps) {
  const [stats, setStats] = useState<RefreshStats | null>(null);
  const [errors, setErrors] = useState<RefreshError[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());
  
  // Update stats periodically
  const updateStats = useCallback(() => {
    const currentStats = refreshOrchestrator.getStats();
    const currentErrors = refreshOrchestrator.getRecentErrors();
    
    setStats(currentStats);
    setErrors(currentErrors);
    setLastUpdate(Date.now());
  }, []);
  
  // Auto-update stats every 2 seconds
  useEffect(() => {
    updateStats();
    const interval = setInterval(updateStats, 2000);
    return () => clearInterval(interval);
  }, [updateStats]);
  
  // Listen for orchestrator events for real-time updates
  useEffect(() => {
    const handleEvent = () => {
      updateStats();
    };
    
    const events = [
      'request-received',
      'execution-completed',
      'execution-failed',
      'duplicate-prevented',
      'batch-processing'
    ];
    
    events.forEach(event => {
      refreshOrchestrator.addEventListener(event, handleEvent);
    });
    
    return () => {
      events.forEach(event => {
        refreshOrchestrator.removeEventListener(event, handleEvent);
      });
    };
  }, [updateStats]);
  
  if (!stats) {
    return null;
  }
  
  const successRate = stats.totalRequests > 0 
    ? Math.round((stats.completedRequests / stats.totalRequests) * 100)
    : 100;
  
  const hasErrors = errors.length > 0;
  const hasActivity = stats.totalRequests > 0;
  
  // Simple indicator mode
  if (!showDetails && !isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`fixed bottom-4 right-4 p-2 rounded-full shadow-lg transition-all hover:scale-110 z-50 ${
          hasErrors 
            ? 'bg-red-500 text-white' 
            : hasActivity 
              ? 'bg-green-500 text-white'
              : 'bg-gray-500 text-white'
        } ${className}`}
        title={`Refresh Monitor - ${stats.totalRequests} requests, ${successRate}% success`}
      >
        <Activity size={16} />
      </button>
    );
  }
  
  return (
    <div className={`fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Activity size={16} className="text-blue-500" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            Refresh Monitor
          </span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X size={14} />
        </button>
      </div>
      
      {/* Stats Grid */}
      <div className="p-3 space-y-3">
        {/* Primary Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
            <div className="text-blue-600 dark:text-blue-400 font-medium">Total Requests</div>
            <div className="text-lg font-bold text-blue-800 dark:text-blue-300">
              {stats.totalRequests}
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
            <div className="text-green-600 dark:text-green-400 font-medium">Success Rate</div>
            <div className="text-lg font-bold text-green-800 dark:text-green-300">
              {successRate}%
            </div>
          </div>
        </div>
        
        {/* Performance Metrics */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
            <div className="text-purple-600 dark:text-purple-400 font-medium">Avg Time</div>
            <div className="text-sm font-bold text-purple-800 dark:text-purple-300">
              {Math.round(stats.averageExecutionTime)}ms
            </div>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-900/20 p-2 rounded">
            <div className="text-orange-600 dark:text-orange-400 font-medium">Duplicates Prevented</div>
            <div className="text-sm font-bold text-orange-800 dark:text-orange-300">
              {stats.duplicatesPreventedCount}
            </div>
          </div>
        </div>
        
        {/* Efficiency Indicators */}
        <div className="flex justify-between items-center text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-1">
            <CheckCircle size={12} className="text-green-500" />
            <span>{stats.completedRequests} completed</span>
          </div>
          
          {stats.failedRequests > 0 && (
            <div className="flex items-center space-x-1">
              <AlertTriangle size={12} className="text-red-500" />
              <span>{stats.failedRequests} failed</span>
            </div>
          )}
          
          {stats.batchesProcessed > 0 && (
            <div className="flex items-center space-x-1">
              <Clock size={12} className="text-blue-500" />
              <span>{stats.batchesProcessed} batches</span>
            </div>
          )}
        </div>
        
        {/* Recent Errors */}
        {hasErrors && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
            <div className="text-xs font-medium text-red-600 dark:text-red-400 mb-1">
              Recent Errors ({errors.length})
            </div>
            <div className="max-h-20 overflow-y-auto space-y-1">
              {errors.slice(0, 3).map((error, index) => (
                <div 
                  key={index}
                  className="text-xs bg-red-50 dark:bg-red-900/20 p-1 rounded"
                >
                  <div className="font-medium text-red-700 dark:text-red-400">
                    {error.key}
                  </div>
                  <div className="text-red-600 dark:text-red-500 truncate">
                    {error.error.message}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Performance Optimization Suggestions */}
        {stats.totalRequests > 50 && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Optimization Impact
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              {stats.duplicatesPreventedCount > 0 && (
                <div>• Prevented {stats.duplicatesPreventedCount} duplicate operations</div>
              )}
              {stats.batchesProcessed > 0 && (
                <div>• Processed {stats.batchesProcessed} batches efficiently</div>
              )}
              {stats.conflictsResolved > 0 && (
                <div>• Resolved {stats.conflictsResolved} timing conflicts</div>
              )}
            </div>
          </div>
        )}
        
        {/* Last Update */}
        <div className="text-xs text-gray-500 dark:text-gray-500 text-center border-t border-gray-200 dark:border-gray-700 pt-2">
          Updated {Math.round((Date.now() - lastUpdate) / 1000)}s ago
        </div>
      </div>
    </div>
  );
}

/**
 * Simple refresh performance indicator for production use
 */
export function RefreshIndicator({ className = '' }: { className?: string }) {
  const [stats, setStats] = useState<RefreshStats | null>(null);
  
  useEffect(() => {
    const updateStats = () => {
      setStats(refreshOrchestrator.getStats());
    };
    
    updateStats();
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, []);
  
  if (!stats || stats.totalRequests === 0) {
    return null;
  }
  
  const efficiency = stats.duplicatesPreventedCount > 0 
    ? Math.round((stats.duplicatesPreventedCount / stats.totalRequests) * 100)
    : 0;
  
  return (
    <div className={`inline-flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 ${className}`}>
      <Activity size={12} className="text-green-500" />
      <span>
        {stats.totalRequests} ops, {efficiency}% optimized
      </span>
    </div>
  );
}