"""
File Storage Service for handling image storage in the weld defect detection system.
Implements Option A: Database binary storage with optimizations.
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Tuple, List
from enum import Enum
import io
import time
import hashlib
from PIL import Image, ImageOps
from sqlalchemy.orm import Session

from ..models.database import Capture
from ..config import settings


class ImageFormat(str, Enum):
    """Supported image formats"""
    JPEG = "JPEG"
    PNG = "PNG"
    WEBP = "WEBP"


class ThumbnailSize(str, Enum):
    """Standard thumbnail sizes"""
    SMALL = "small"    # 150x150
    MEDIUM = "medium"  # 300x300
    LARGE = "large"    # 600x600


class StorageMetrics:
    """Storage operation metrics"""
    
    def __init__(self):
        self.operations_count = 0
        self.total_bytes_stored = 0
        self.total_bytes_retrieved = 0
        self.compression_savings = 0
        self.error_count = 0
    
    def record_store(self, original_size: int, compressed_size: int):
        """Record a storage operation"""
        self.operations_count += 1
        self.total_bytes_stored += compressed_size
        self.compression_savings += (original_size - compressed_size)
    
    def record_retrieval(self, size: int):
        """Record a retrieval operation"""
        self.total_bytes_retrieved += size
    
    def record_error(self):
        """Record an error"""
        self.error_count += 1


class BaseFileStorageService(ABC):
    """Abstract base class for file storage services"""
    
    @abstractmethod
    def store_image(self, image_data: bytes, 
                   image_type: str = "original",
                   compress: bool = True,
                   quality: int = 85) -> Dict[str, Any]:
        """Store an image and return metadata"""
        pass
    
    @abstractmethod
    def retrieve_image(self, capture_id: str, image_type: str = "original") -> Optional[bytes]:
        """Retrieve an image by capture ID and type"""
        pass
    
    @abstractmethod
    def delete_images(self, capture_id: str) -> bool:
        """Delete all images for a capture"""
        pass
    
    @abstractmethod
    def generate_thumbnails(self, image_data: bytes, 
                          sizes: List[ThumbnailSize] = None) -> Dict[str, bytes]:
        """Generate multiple thumbnail sizes"""
        pass
    
    @abstractmethod
    def get_storage_info(self, capture_id: str) -> Dict[str, Any]:
        """Get storage information for a capture"""
        pass


class DatabaseFileStorageService(BaseFileStorageService):
    """
    Database-based file storage implementation (Option A)
    Optimized for performance with compression and efficient blob handling
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.metrics = StorageMetrics()
        
        # Default thumbnail sizes
        self.thumbnail_sizes = {
            ThumbnailSize.SMALL: (150, 150),
            ThumbnailSize.MEDIUM: (300, 300), 
            ThumbnailSize.LARGE: (600, 600)
        }
        
        # Compression settings
        self.compression_settings = {
            ImageFormat.JPEG: {"quality": 85, "optimize": True},
            ImageFormat.WEBP: {"quality": 80, "optimize": True},
            ImageFormat.PNG: {"optimize": True, "compress_level": 6}
        }
    
    def store_image(self, image_data: bytes, 
                   image_type: str = "original",
                   compress: bool = True,
                   quality: int = 85) -> Dict[str, Any]:
        """
        Store an image with optional compression
        
        Args:
            image_data: Raw image bytes
            image_type: Type of image (original, processed, thumbnail_small, etc.)
            compress: Whether to compress the image
            quality: Compression quality (1-100)
            
        Returns:
            Dict with storage metadata including size, format, checksum
        """
        try:
            original_size = len(image_data)
            processed_data = image_data
            image_format = "JPEG"  # Default format
            
            if compress:
                processed_data, image_format = self._compress_image(image_data, quality)
            
            # Calculate checksum for integrity verification
            checksum = hashlib.sha256(processed_data).hexdigest()
            
            # Record metrics
            compressed_size = len(processed_data)
            self.metrics.record_store(original_size, compressed_size)
            
            return {
                "data": processed_data,
                "size": compressed_size,
                "original_size": original_size,
                "format": image_format,
                "checksum": checksum,
                "compression_ratio": original_size / compressed_size if compressed_size > 0 else 1,
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            self.metrics.record_error()
            raise RuntimeError(f"Failed to store image: {str(e)}")
    
    def retrieve_image(self, capture_id: str, image_type: str = "original") -> Optional[bytes]:
        """
        Retrieve an image from database storage
        
        Args:
            capture_id: ID of the capture
            image_type: Type of image to retrieve
            
        Returns:
            Image bytes or None if not found
        """
        try:
            capture = self.db.query(Capture).filter(Capture.capture_id == capture_id).first()
            if not capture:
                return None
            
            image_data = None
            if image_type == "original":
                image_data = capture.original_image_blob
            elif image_type == "processed":
                image_data = capture.processed_image_blob
            elif image_type.startswith("thumbnail"):
                image_data = capture.thumbnail_blob
            
            if image_data:
                self.metrics.record_retrieval(len(image_data))
            
            return image_data
            
        except Exception as e:
            self.metrics.record_error()
            raise RuntimeError(f"Failed to retrieve image: {str(e)}")
    
    def delete_images(self, capture_id: str) -> bool:
        """
        Delete all images for a capture by setting blob fields to NULL
        
        Args:
            capture_id: ID of the capture
            
        Returns:
            True if successful, False if capture not found
        """
        try:
            capture = self.db.query(Capture).filter(Capture.capture_id == capture_id).first()
            if not capture:
                return False
            
            # Clear all image blob fields
            capture.original_image_blob = None
            capture.processed_image_blob = None
            capture.thumbnail_blob = None
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.metrics.record_error()
            self.db.rollback()
            raise RuntimeError(f"Failed to delete images: {str(e)}")
    
    def generate_thumbnails(self, image_data: bytes, 
                          sizes: List[ThumbnailSize] = None) -> Dict[str, bytes]:
        """
        Generate multiple thumbnail sizes from an image
        
        Args:
            image_data: Original image bytes
            sizes: List of thumbnail sizes to generate
            
        Returns:
            Dictionary mapping size names to thumbnail bytes
        """
        if sizes is None:
            sizes = [ThumbnailSize.SMALL, ThumbnailSize.MEDIUM, ThumbnailSize.LARGE]
        
        thumbnails = {}
        
        try:
            # Open the original image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary (for PNG with transparency, etc.)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Generate each requested thumbnail size
            for size_enum in sizes:
                if size_enum in self.thumbnail_sizes:
                    target_size = self.thumbnail_sizes[size_enum]
                    thumbnail = self._create_thumbnail(image, target_size)
                    thumbnails[size_enum.value] = thumbnail
            
            return thumbnails
            
        except Exception as e:
            self.metrics.record_error()
            raise RuntimeError(f"Failed to generate thumbnails: {str(e)}")
    
    def get_storage_info(self, capture_id: str) -> Dict[str, Any]:
        """
        Get comprehensive storage information for a capture
        
        Args:
            capture_id: ID of the capture
            
        Returns:
            Dictionary with storage metrics and information
        """
        try:
            capture = self.db.query(Capture).filter(Capture.capture_id == capture_id).first()
            if not capture:
                return {"error": "Capture not found"}
            
            info = {
                "capture_id": capture_id,
                "images": {
                    "original": {
                        "exists": capture.original_image_blob is not None,
                        "size": len(capture.original_image_blob) if capture.original_image_blob else 0
                    },
                    "processed": {
                        "exists": capture.processed_image_blob is not None,
                        "size": len(capture.processed_image_blob) if capture.processed_image_blob else 0
                    },
                    "thumbnail": {
                        "exists": capture.thumbnail_blob is not None,
                        "size": len(capture.thumbnail_blob) if capture.thumbnail_blob else 0
                    }
                },
                "total_size": 0,
                "created_at": capture.capture_timestamp
            }
            
            # Calculate total size
            for image_info in info["images"].values():
                info["total_size"] += image_info["size"]
            
            return info
            
        except Exception as e:
            self.metrics.record_error()
            return {"error": f"Failed to get storage info: {str(e)}"}
    
    def _compress_image(self, image_data: bytes, quality: int = 85) -> Tuple[bytes, str]:
        """
        Compress an image using PIL with optimal settings
        
        Args:
            image_data: Original image bytes
            quality: Compression quality (1-100)
            
        Returns:
            Tuple of (compressed_bytes, format)
        """
        try:
            # Open the image
            image = Image.open(io.BytesIO(image_data))
            
            # Auto-orient the image based on EXIF data
            image = ImageOps.exif_transpose(image)
            
            # Convert to RGB if necessary
            if image.mode not in ('RGB', 'L'):
                image = image.convert('RGB')
            
            # Choose optimal format based on image characteristics
            # Use WEBP for better compression, fallback to JPEG
            target_format = ImageFormat.WEBP
            
            # Compress the image
            output = io.BytesIO()
            compression_settings = self.compression_settings[target_format].copy()
            compression_settings["quality"] = quality
            
            image.save(output, format=target_format.value, **compression_settings)
            compressed_data = output.getvalue()
            
            return compressed_data, target_format.value
            
        except Exception as e:
            # Fallback to original data if compression fails
            return image_data, "UNKNOWN"
    
    def _create_thumbnail(self, image: Image.Image, size: Tuple[int, int]) -> bytes:
        """
        Create a thumbnail of specified size
        
        Args:
            image: PIL Image object
            size: Target size tuple (width, height)
            
        Returns:
            Thumbnail image bytes
        """
        # Create a copy to avoid modifying the original
        thumbnail_image = image.copy()
        
        # Use high-quality resampling
        thumbnail_image.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Save as JPEG with good quality
        output = io.BytesIO()
        thumbnail_image.save(output, format='JPEG', quality=85, optimize=True)
        
        return output.getvalue()
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current storage metrics"""
        return {
            "operations_count": self.metrics.operations_count,
            "total_bytes_stored": self.metrics.total_bytes_stored,
            "total_bytes_retrieved": self.metrics.total_bytes_retrieved,
            "compression_savings": self.metrics.compression_savings,
            "error_count": self.metrics.error_count,
            "compression_ratio": (
                self.metrics.compression_savings / self.metrics.total_bytes_stored 
                if self.metrics.total_bytes_stored > 0 else 0
            )
        }
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the storage system
        
        Returns:
            Health status and metrics
        """
        try:
            # Test basic database connectivity
            test_query = self.db.execute("SELECT 1").fetchone()
            if not test_query:
                return {"status": "unhealthy", "error": "Database connectivity failed"}
            
            # Test image processing capabilities
            test_image_data = self._create_test_image()
            result = self.store_image(test_image_data, "test", compress=True)
            
            return {
                "status": "healthy",
                "database_connected": True,
                "image_processing": True,
                "compression_working": result["compression_ratio"] > 1,
                "metrics": self.get_metrics()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "metrics": self.get_metrics()
            }
    
    def _create_test_image(self) -> bytes:
        """Create a small test image for health checks"""
        # Create a simple 100x100 red square
        image = Image.new('RGB', (100, 100), color='red')
        output = io.BytesIO()
        image.save(output, format='JPEG')
        return output.getvalue()


# Factory function for creating storage service instances
def create_file_storage_service(db: Session) -> BaseFileStorageService:
    """
    Factory function to create the appropriate file storage service
    Currently returns DatabaseFileStorageService (Option A)
    """
    return DatabaseFileStorageService(db)