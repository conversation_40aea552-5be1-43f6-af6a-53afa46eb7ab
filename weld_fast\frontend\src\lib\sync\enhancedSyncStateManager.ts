'use client';

import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import type {
  SyncStats,
  SyncProgress,
  ItemUpdate,
  FrameState,
  EnhancedSyncState,
  SyncStatus,
  SyncObjectType
} from './types';

// Simple debounce implementation
function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Enhanced Sync State Manager
 * 
 * Provides reactive state management for sync operations with granular item tracking.
 * Eliminates the need for polling by providing immediate updates when items change.
 */
class EnhancedSyncStateManager {
  private static instance: EnhancedSyncStateManager;
  
  // Initial state
  private readonly initialState: EnhancedSyncState = {
    stats: { pending: 0, processing: 0, completed: 0, failed: 0 },
    progress: null,
    isProcessing: false,
    lastUpdated: Date.now(),
    itemUpdates: [],
    frameStates: new Map()
  };

  // BehaviorSubject holds the current sync state
  private syncState = new BehaviorSubject<EnhancedSyncState>(this.initialState);

  // Debounced state update to prevent excessive emissions
  private debouncedUpdate = debounce(((state: Partial<EnhancedSyncState>) => {
    const currentState = this.syncState.value;
    const newState: EnhancedSyncState = {
      ...currentState,
      ...state,
      lastUpdated: Date.now()
    };
    this.syncState.next(newState);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }) as any, 100) as (state: Partial<EnhancedSyncState>) => void; // Type assertion for more responsive updates

  private constructor() {}

  public static getInstance(): EnhancedSyncStateManager {
    if (!EnhancedSyncStateManager.instance) {
      EnhancedSyncStateManager.instance = new EnhancedSyncStateManager();
    }
    return EnhancedSyncStateManager.instance;
  }

  /**
   * Update sync state with item-level changes
   */
  public updateSyncState(stats: SyncStats, progress?: SyncProgress | null): void {
    const currentState = this.syncState.value;
    const newProcessing = progress?.isProcessing ?? false;
    const forceUpdate = currentState.isProcessing !== newProcessing;

    console.log('[EnhancedSyncStateManager] Updating sync state:', {
      stats,
      progress,
      forceUpdate
    });

    this.debouncedUpdate({
      stats,
      progress: progress ?? null,
      isProcessing: newProcessing
    });
  }

  /**
   * Update individual item sync status
   */
  public updateItemSyncStatus(
    frameId: string,
    itemId: string,
    itemType: SyncObjectType,
    syncStatus: SyncStatus
  ): void {
    const currentState = this.syncState.value;
    
    // Add item update
    const itemUpdate: ItemUpdate = {
      frameId,
      itemId,
      itemType,
      syncStatus,
      timestamp: Date.now()
    };

    // Update frame state
    const updatedFrameStates = new Map(currentState.frameStates);
    const frameState = updatedFrameStates.get(frameId) || {
      pending: 0,
      synced: 0,
      failed: 0,
      lastUpdated: 0
    };

    // Update frame stats based on sync status change
    if (syncStatus === 'synced') {
      frameState.synced++;
      frameState.pending = Math.max(0, frameState.pending - 1);
    } else if (syncStatus === 'pending') {
      frameState.pending++;
    } else if (syncStatus === 'conflict') {
      frameState.failed++;
      frameState.pending = Math.max(0, frameState.pending - 1);
    }

    frameState.lastUpdated = Date.now();
    updatedFrameStates.set(frameId, frameState);

    console.log('[EnhancedSyncStateManager] Item sync status updated:', {
      frameId,
      itemId,
      itemType,
      syncStatus,
      frameState
    });

    // Force immediate update for item changes
    const newState: EnhancedSyncState = {
      ...currentState,
      itemUpdates: [...currentState.itemUpdates, itemUpdate].slice(-100), // Keep last 100
      frameStates: updatedFrameStates,
      lastUpdated: Date.now()
    };

    this.syncState.next(newState);
  }

  /**
   * Get observable sync state
   */
  public getSyncState(): Observable<EnhancedSyncState> {
    return this.syncState.asObservable().pipe(
      distinctUntilChanged((prev, curr) => 
        prev.lastUpdated === curr.lastUpdated
      )
    );
  }

  /**
   * Get observable frame-specific state
   */
  public getFrameState(frameId: string): Observable<FrameState | null> {
    return this.syncState.asObservable().pipe(
      map(state => state.frameStates.get(frameId) || null),
      distinctUntilChanged((prev, curr) => 
        JSON.stringify(prev) === JSON.stringify(curr)
      )
    );
  }

  /**
   * Get observable item updates for a specific frame
   */
  public getFrameItemUpdates(frameId: string): Observable<ItemUpdate[]> {
    return this.syncState.asObservable().pipe(
      map(state => state.itemUpdates.filter(update => update.frameId === frameId)),
      distinctUntilChanged((prev, curr) => 
        JSON.stringify(prev) === JSON.stringify(curr)
      )
    );
  }

  /**
   * Get current sync state snapshot
   */
  public getCurrentState(): EnhancedSyncState {
    return this.syncState.value;
  }

  /**
   * Get current frame state snapshot
   */
  public getCurrentFrameState(frameId: string): FrameState | null {
    return this.syncState.value.frameStates.get(frameId) || null;
  }

  /**
   * Get current sync progress snapshot
   */
  public getCurrentSyncProgress(): SyncProgress | null {
    return this.syncState.value.progress;
  }

  /**
   * Update sync progress only (for real-time progress updates during sync)
   */
  public updateSyncProgress(progress: SyncProgress): void {
    const currentState = this.syncState.value;
    
    // Force immediate update for progress changes
    const newState: EnhancedSyncState = {
      ...currentState,
      progress,
      isProcessing: progress.isProcessing,
      lastUpdated: Date.now()
    };

    this.syncState.next(newState);
  }

  /**
   * Reset sync state to initial values
   */
  public resetState(): void {
    this.syncState.next(this.initialState);
  }

  /**
   * Clear old item updates
   */
  public clearOldItemUpdates(olderThan: number = 300000): void { // 5 minutes
    const currentState = this.syncState.value;
    const cutoff = Date.now() - olderThan;
    
    const filteredUpdates = currentState.itemUpdates.filter(
      update => update.timestamp > cutoff
    );

    if (filteredUpdates.length !== currentState.itemUpdates.length) {
      this.syncState.next({
        ...currentState,
        itemUpdates: filteredUpdates,
        lastUpdated: Date.now()
      });
    }
  }
}

// Export singleton instance
export const enhancedSyncStateManager = EnhancedSyncStateManager.getInstance();

// Export class for testing
export { EnhancedSyncStateManager };

// Re-export types for convenience
export type {
  SyncStats,
  SyncProgress,
  ItemUpdate,
  FrameState,
  EnhancedSyncState,
  SyncStatus,
  SyncObjectType
} from './types';
