'use client';

import { useCallback, useEffect, useRef } from 'react';
import { refreshOrchestrator } from '@/lib/refresh/RefreshOrchestrator';
import { RefreshPriority, RefreshTrigger } from '@/lib/refresh/types';

interface UseOrchestratedRefreshOptions {
  // Core options
  key: string;                    // Unique key for this refresh operation
  enabled?: boolean;              // Whether refresh is enabled
  priority?: RefreshPriority;     // Priority level for coordination
  
  // Timing options
  debounceMs?: number;           // Debounce time for this specific refresh
  cooldownMs?: number;           // Minimum time between executions
  maxRetries?: number;           // Maximum retry attempts
  
  // Trigger configuration
  triggers?: {
    windowFocus?: boolean;       // Refresh on window focus/visibility
    syncEvents?: boolean;        // Refresh on sync completion
    crossTab?: boolean;          // Refresh on cross-tab updates
    manual?: boolean;            // Allow manual refresh calls
  };
  
  // Cross-tab communication
  broadcastChannel?: string;     // Channel for cross-tab coordination
  
  // Context data
  context?: Record<string, unknown>; // Additional context to pass with requests
  
  // Event callbacks
  onRefreshStart?: () => void;
  onRefreshComplete?: () => void;
  onRefreshError?: (error: Error) => void;
  onDuplicatePrevented?: () => void;
}

interface OrchestratedRefreshReturn {
  refresh: (trigger?: RefreshTrigger) => Promise<void>;
  isRefreshing: boolean;
  lastRefreshTime: number | null;
  refreshCount: number;
  errorCount: number;
  duplicatesPreventedCount: number;
}

/**
 * Coordinated refresh hook that prevents conflicts and optimizes performance
 * Replaces the need for multiple separate refresh hooks
 */
export function useOrchestratedRefresh(
  refreshCallback: () => Promise<void>,
  options: UseOrchestratedRefreshOptions
): OrchestratedRefreshReturn {
  const {
    key,
    enabled = true,
    priority = 'medium',
    debounceMs,
    cooldownMs,
    maxRetries,
    triggers = {
      windowFocus: false,
      syncEvents: false,
      crossTab: false,
      manual: true
    },
    broadcastChannel,
    context,
    onRefreshStart,
    onRefreshComplete,
    onRefreshError,
    onDuplicatePrevented
  } = options;
  
  // State tracking
  const isRefreshingRef = useRef(false);
  const lastRefreshTimeRef = useRef<number | null>(null);
  const refreshCountRef = useRef(0);
  const errorCountRef = useRef(0);
  const duplicatesPreventedCountRef = useRef(0);
  
  // Stable callback reference
  const stableCallback = useCallback(refreshCallback, [refreshCallback]);
  
  // Main refresh function
  const refresh = useCallback(async (trigger: RefreshTrigger = 'user-action'): Promise<void> => {
    if (!enabled) return;
    
    try {
      isRefreshingRef.current = true;
      onRefreshStart?.();
      
      await refreshOrchestrator.requestRefresh(key, stableCallback, {
        priority,
        trigger,
        debounceMs,
        cooldownMs,
        maxRetries,
        context
      });
      
      refreshCountRef.current++;
      lastRefreshTimeRef.current = Date.now();
      onRefreshComplete?.();
      
    } catch (error) {
      errorCountRef.current++;
      onRefreshError?.(error as Error);
      throw error;
    } finally {
      isRefreshingRef.current = false;
    }
  }, [
    enabled,
    key,
    stableCallback,
    priority,
    debounceMs,
    cooldownMs,
    maxRetries,
    context,
    onRefreshStart,
    onRefreshComplete,
    onRefreshError
  ]);
  
  // Window focus and visibility handling
  useEffect(() => {
    if (!enabled || !triggers.windowFocus) return;
    
    const handleFocus = () => {
      refresh('window-focus').catch(() => {
        // Silently handle errors for background refreshes
      });
    };
    
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refresh('window-focus').catch(() => {
          // Silently handle errors for background refreshes
        });
      }
    };
    
    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, triggers.windowFocus, refresh]);
  
  // Sync events handling
  useEffect(() => {
    if (!enabled || !triggers.syncEvents) return;
    
    const handleSyncComplete = () => {
      refresh('sync-complete').catch(() => {
        // Silently handle errors for background refreshes
      });
    };
    
    // Listen for sync completion events
    refreshOrchestrator.addEventListener('sync-completed', handleSyncComplete);
    
    return () => {
      refreshOrchestrator.removeEventListener('sync-completed', handleSyncComplete);
    };
  }, [enabled, triggers.syncEvents, refresh]);
  
  // Cross-tab communication handling
  useEffect(() => {
    if (!enabled || !triggers.crossTab || !broadcastChannel) return;
    
    let broadcastChannelInstance: BroadcastChannel | null = null;
    
    // Modern browsers support BroadcastChannel
    if (typeof BroadcastChannel !== 'undefined') {
      broadcastChannelInstance = new BroadcastChannel(broadcastChannel);
      
      const handleMessage = (event: MessageEvent) => {
        if (event.data?.type === 'refresh-trigger') {
          refresh('cross-tab').catch(() => {
            // Silently handle errors for background refreshes
          });
        }
      };
      
      broadcastChannelInstance.addEventListener('message', handleMessage);
      
      return () => {
        broadcastChannelInstance?.removeEventListener('message', handleMessage);
        broadcastChannelInstance?.close();
      };
    } else {
      // Fallback to localStorage events for older browsers
      const handleStorageChange = (event: StorageEvent) => {
        if (event.key === `refresh-${broadcastChannel}` && event.newValue) {
          refresh('cross-tab').catch(() => {
            // Silently handle errors for background refreshes
          });
        }
      };
      
      window.addEventListener('storage', handleStorageChange);
      
      return () => {
        window.removeEventListener('storage', handleStorageChange);
      };
    }
  }, [enabled, triggers.crossTab, broadcastChannel, refresh]);
  
  // Orchestrator event listeners for statistics
  useEffect(() => {
    const handleDuplicatePrevented = (data: unknown) => {
      const eventData = data as { request?: { key?: string } };
      if (eventData.request?.key === key) {
        duplicatesPreventedCountRef.current++;
        onDuplicatePrevented?.();
      }
    };
    
    refreshOrchestrator.addEventListener('duplicate-prevented', handleDuplicatePrevented);
    
    return () => {
      refreshOrchestrator.removeEventListener('duplicate-prevented', handleDuplicatePrevented);
    };
  }, [key, onDuplicatePrevented]);
  
  return {
    refresh,
    isRefreshing: isRefreshingRef.current,
    lastRefreshTime: lastRefreshTimeRef.current,
    refreshCount: refreshCountRef.current,
    errorCount: errorCountRef.current,
    duplicatesPreventedCount: duplicatesPreventedCountRef.current
  };
}

/**
 * Utility function to broadcast refresh trigger to other tabs
 */
export function broadcastRefreshTrigger(channel: string, data?: unknown): void {
  if (typeof BroadcastChannel !== 'undefined') {
    const broadcastChannel = new BroadcastChannel(channel);
    broadcastChannel.postMessage({
      type: 'refresh-trigger',
      timestamp: Date.now(),
      data
    });
    broadcastChannel.close();
  } else {
    // Fallback to localStorage
    localStorage.setItem(`refresh-${channel}`, JSON.stringify({
      timestamp: Date.now(),
      data
    }));
    // Clear immediately to trigger storage event
    localStorage.removeItem(`refresh-${channel}`);
  }
}

/**
 * Preset configurations for common refresh scenarios
 */
export const REFRESH_PRESETS = {
  // High priority, immediate refresh for user actions
  USER_ACTION: {
    priority: 'high' as RefreshPriority,
    debounceMs: 50,
    cooldownMs: 100,
    maxRetries: 3,
    triggers: {
      manual: true,
      windowFocus: false,
      syncEvents: false,
      crossTab: true
    }
  },
  
  // Medium priority for automatic refreshes
  AUTO_REFRESH: {
    priority: 'medium' as RefreshPriority,
    debounceMs: 200,
    cooldownMs: 500,
    maxRetries: 2,
    triggers: {
      manual: true,
      windowFocus: true,
      syncEvents: true,
      crossTab: true
    }
  },
  
  // Low priority for background updates
  BACKGROUND_SYNC: {
    priority: 'low' as RefreshPriority,
    debounceMs: 1000,
    cooldownMs: 5000,
    maxRetries: 1,
    triggers: {
      manual: false,
      windowFocus: false,
      syncEvents: true,
      crossTab: false
    }
  }
} as const;