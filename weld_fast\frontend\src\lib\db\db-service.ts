// src/services/db/db-service.ts

/**
 * Database configuration
 */
export const DB_CONFIG = {
    name: 'WeldDetectionDB',
    version: 1,
    stores: {
      frames: 'frameId',
      captures: 'captureId, frameId, syncStatus',
      syncQueue: '++queueId, objectType, objectId, status, priority'
    }
  };
  
  /**
   * Type definitions for database schema
   */
  export interface Frame {
    frameId: string;
    modelNumber: string;
    machineSerialNumber: string;
    inspectorName: string;
    creationTimestamp: number;
    lastModifiedTimestamp: number;
    status: 'active' | 'completed' | 'archived';
    captureCount: number;
    metadata?: Record<string, unknown>;
    syncStatus: 'synced' | 'pending' | 'conflict';
    lastSyncedAt?: number;
  }
  
  export interface Capture {
    captureId: string;
    frameId: string;
    captureTimestamp: number;
    originalImageBlob?: Blob;
    processedImageBlob?: Blob;
    thumbnailBlob?: Blob;
    detectionResults: DetectionResult[];
    syncStatus: 'synced' | 'pending' | 'conflict';
    syncVersion: number;
    lastSyncAttempt?: number;
  }
  
  export interface DetectionResult {
    class: string;
    confidence: number;
    bbox: [number, number, number, number]; // [x1, y1, x2, y2]
  }
  
  export interface SyncQueueItem {
    queueId?: number; // Auto-incremented
    operationType: 'create' | 'update' | 'delete';
    objectType: 'frame' | 'capture';
    objectId: string;
    data?: unknown;
    priority: number;
    createdAt: number;
    attemptCount: number;
    lastAttempt?: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
  }
  
  /**
   * Database connection instance
   */
  let dbInstance: IDBDatabase | null = null;
  
  /**
   * Initialize the database connection
   */
  export const initDatabase = async (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      if (dbInstance) {
        return resolve(dbInstance);
      }
  
      const request = indexedDB.open(DB_CONFIG.name, DB_CONFIG.version);
  
      // Handle database upgrade (schema creation/migration)
      request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
        const db = (event.target as IDBOpenDBRequest).result;
        // const transaction = (event.target as IDBOpenDBRequest).transaction;
        
        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains('frames')) {
          const framesStore = db.createObjectStore('frames', { keyPath: 'frameId' });
          framesStore.createIndex('byMachineAndInspector', ['machineSerialNumber', 'inspectorName'], { unique: false });
          framesStore.createIndex('byStatus', 'status', { unique: false });
          framesStore.createIndex('bySyncStatus', 'syncStatus', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('captures')) {
          const capturesStore = db.createObjectStore('captures', { keyPath: 'captureId' });
          capturesStore.createIndex('byFrameId', 'frameId', { unique: false });
          capturesStore.createIndex('bySyncStatus', 'syncStatus', { unique: false });
          capturesStore.createIndex('byFrameAndTimestamp', ['frameId', 'captureTimestamp'], { unique: false });
        }
        
        if (!db.objectStoreNames.contains('syncQueue')) {
          const syncQueueStore = db.createObjectStore('syncQueue', { keyPath: 'queueId', autoIncrement: true });
          syncQueueStore.createIndex('byStatus', 'status', { unique: false });
          syncQueueStore.createIndex('byStatusAndPriority', ['status', 'priority'], { unique: false });
          syncQueueStore.createIndex('byObjectReference', ['objectType', 'objectId'], { unique: false });
        }
        
        console.log('Database schema created or updated');
      };
  
      request.onsuccess = (event: Event) => {
        dbInstance = (event.target as IDBOpenDBRequest).result;
        console.log('Database connection established');
        resolve(dbInstance);
      };
  
      request.onerror = (event: Event) => {
        console.error('Database error:', (event.target as IDBOpenDBRequest).error);
        reject((event.target as IDBOpenDBRequest).error);
      };
    });
  };
  
  /**
   * Get a transaction for a specific store
   */
  export const getTransaction = async (
    storeName: string, 
    mode: IDBTransactionMode = 'readonly'
  ): Promise<IDBTransaction> => {
    const db = await initDatabase();
    return db.transaction(storeName, mode);
  };
  
  /**
   * Get an object store for operations
   */
  export const getObjectStore = async (
    storeName: string, 
    mode: IDBTransactionMode = 'readonly'
  ): Promise<IDBObjectStore> => {
    const transaction = await getTransaction(storeName, mode);
    return transaction.objectStore(storeName);
  };
  
  /**
   * Clear all data in a specific store
   */
  export const clearStore = async (storeName: string): Promise<void> => {
    const store = await getObjectStore(storeName, 'readwrite');
    return new Promise((resolve, reject) => {
      const request = store.clear();
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  };
  
  /**
   * Get the estimated size of the database
   */
  export const getDatabaseSize = async (): Promise<number> => {
    // This is an estimate, as there's no direct way to get the size
    // We sum up the sizes of all blob objects
    
    let totalSize = 0;
    
    // Get all captures
    const captures = await getAllCaptures();
    
    // Sum the sizes of all blobs in each capture
    for (const capture of captures) {
      if (capture.originalImageBlob) {
        totalSize += capture.originalImageBlob.size;
      }
      if (capture.processedImageBlob) {
        totalSize += capture.processedImageBlob.size;
      }
      if (capture.thumbnailBlob) {
        totalSize += capture.thumbnailBlob.size;
      }
    }
    
    // Add a fixed amount for JSON data (frames, metadata, etc.)
    const frameCount = (await getAllFrames()).length;
    totalSize += frameCount * 5000; // Rough estimate of JSON data size
    
    return totalSize;
  };
  
  /**
   * Helper functions to be imported in repositories
   */
  export const getAllFrames = async (): Promise<Frame[]> => {
    const store = await getObjectStore('frames');
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  };
  
  export const getAllCaptures = async (): Promise<Capture[]> => {
    const store = await getObjectStore('captures');
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  };