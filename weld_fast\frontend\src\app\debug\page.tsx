'use client';

import { useAuth } from '@/context/AuthContext';

export default function DebugPage() {
  const { user, tokens, isAuthenticated, isLoading } = useAuth();

  const checkCookies = () => {
    const cookies = document.cookie.split(';').reduce((acc: Record<string, string>, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {});
    console.log('Cookies:', cookies);
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Auth Debug Page</h1>
      
      <div className="space-y-4">
        <div>
          <strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>User:</strong> 
          <pre className="bg-gray-100 p-2 mt-1">{JSON.stringify(user, null, 2)}</pre>
        </div>
        <div>
          <strong>Tokens:</strong>
          <pre className="bg-gray-100 p-2 mt-1">{JSON.stringify(tokens, null, 2)}</pre>
        </div>
        
        <button 
          onClick={checkCookies}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Check Cookies (Console)
        </button>
      </div>
    </div>
  );
}