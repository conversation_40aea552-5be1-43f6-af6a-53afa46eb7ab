# Technical Architecture Documentation

This document provides a comprehensive technical overview of the Weld Defect Detection System architecture, designed for architects, senior developers, and technical stakeholders.

## Table of Contents

- [System Overview](#system-overview)
- [High-Level Architecture](#high-level-architecture)
- [Database Architecture](#database-architecture)
- [Sync System Architecture](#sync-system-architecture)
- [AI/ML Pipeline](#aiml-pipeline)
- [Authentication & Security](#authentication--security)
- [Offline-First Design](#offline-first-design)
- [PWA Implementation](#pwa-implementation)
- [API Design Patterns](#api-design-patterns)
- [Performance Considerations](#performance-considerations)
- [Technology Choices & Trade-offs](#technology-choices--trade-offs)
- [Scalability Strategy](#scalability-strategy)
- [Future Evolution](#future-evolution)

## System Overview

The Weld Defect Detection System is an offline-first web application that provides real-time AI-powered defect detection for industrial welding operations. The system follows a modern, distributed architecture with client-side AI inference and robust offline capabilities.

### Core Architecture Principles

1. **Offline-First**: All operations work without network connectivity
2. **Client-Side AI**: TensorFlow.js inference eliminates server dependency for detection
3. **Progressive Enhancement**: Graceful degradation from online to offline modes
4. **Reactive Architecture**: Event-driven updates with minimal polling
5. **Production-Ready**: Enterprise-grade sync, authentication, and error handling

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend (Next.js 15)                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   React UI      │  │  TensorFlow.js  │  │   Service       │  │
│  │   Components    │  │   YOLOv8n       │  │   Worker        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Session Mgmt   │  │   IndexedDB     │  │  Reactive Sync  │  │
│  │  (Context API)  │  │   (Dexie.js)    │  │   (RxJS)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                        ┌───────▼───────┐
                        │   HTTP/REST   │
                        │   JSON/Form   │
                        └───────┬───────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      Backend (FastAPI)                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   API Routes    │  │  Authentication │  │   File Storage  │  │
│  │  (REST/JSON)    │  │   (JWT + BCrypt)│  │   (Binary)      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Sync Service   │  │  SQLAlchemy ORM │  │   SQLite        │  │
│  │ (Batch + Error) │  │   (Async)       │  │  (Production)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### Layer Responsibilities

**Frontend Layer**:
- User interface and interaction management
- Client-side AI inference with TensorFlow.js
- Local data storage and caching (IndexedDB)
- Offline-first sync queue management
- Progressive Web App features

**Backend Layer**:
- RESTful API endpoints
- Authentication and authorization
- Data persistence and synchronization
- File storage and processing
- Analytics and reporting

## Database Architecture

The system employs a dual-database architecture optimized for offline-first operations:

### Client-Side: IndexedDB Schema

```typescript
interface WeldDetectionDB {
  // Detection Sessions
  frames: {
    frameId: string;           // UUID primary key
    modelNumber: string;
    machineSerialNumber: string;
    inspectorName: string;
    creationTimestamp: number;
    lastModifiedTimestamp: number;
    status: 'active' | 'completed' | 'archived';
    captureCount: number;
    metadata?: Record<string, unknown>;
    syncStatus: 'synced' | 'pending' | 'conflict';
    lastSyncedAt?: number;
  };

  // Individual Detections
  captures: {
    captureId: string;         // UUID primary key
    frameId: string;           // Foreign key to frames
    captureTimestamp: number;
    originalImageBlob?: Blob;  // Raw camera input
    processedImageBlob?: Blob; // With detection overlays
    thumbnailBlob?: Blob;      // Optimized preview
    detectionResults: DetectionResult[];
    syncStatus: 'synced' | 'pending' | 'conflict';
    syncVersion: number;       // Optimistic concurrency
    lastSyncAttempt?: number;
  };

  // Background Sync Management
  syncQueue: {
    queueId?: number;          // Auto-increment
    operationType: 'create' | 'update' | 'delete';
    objectType: 'frame' | 'capture';
    objectId: string;
    priority: number;          // Sync ordering
    createdAt: number;
    attemptCount: number;      // Retry logic
    lastAttempt?: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    context?: {
      frameId?: string;
      [key: string]: unknown;
    };
    errorDetails?: {
      message?: string;
      errorType?: 'network' | 'auth' | 'server' | 'client' | 'unknown';
      finalAttempt?: boolean;
      retryScheduled?: boolean;
      nextRetryAt?: number;
    };
  };
}
```

### Server-Side: SQLite Schema (SQLAlchemy)

```python
# User Management
class User(Base):
    user_id: str = Column(String, primary_key=True)
    username: str = Column(String, unique=True, index=True)
    email: str = Column(String, unique=True, index=True)
    full_name: str = Column(String)
    hashed_password: str = Column(String)
    role: UserRole = Column(Enum(UserRole))
    is_active: bool = Column(Boolean, default=True)
    created_at: int = Column(Integer)
    last_login: int = Column(Integer, nullable=True)

# Detection Sessions
class Frame(Base):
    frame_id: str = Column(String, primary_key=True)
    model_number: str = Column(String)
    machine_serial_number: str = Column(String)
    inspector_id: str = Column(String, ForeignKey("users.user_id"))
    inspector_name: str = Column(String)  # Backward compatibility
    creation_timestamp: int = Column(Integer)
    last_modified_timestamp: int = Column(Integer)
    status: FrameStatus = Column(Enum(FrameStatus))
    capture_count: int = Column(Integer, default=0)
    frame_metadata: dict = Column(JSON, nullable=True)
    sync_status: SyncStatus = Column(Enum(SyncStatus))
    last_synced_at: int = Column(Integer, nullable=True)

# Individual Detections
class Capture(Base):
    capture_id: str = Column(String, primary_key=True)
    frame_id: str = Column(String, ForeignKey("frames.frame_id"))
    capture_timestamp: int = Column(Integer)
    original_image_blob: bytes = Column(LargeBinary, nullable=True)
    processed_image_blob: bytes = Column(LargeBinary, nullable=True)
    thumbnail_blob: bytes = Column(LargeBinary, nullable=True)
    detection_results: list = Column(JSON, default=list)
    sync_status: SyncStatus = Column(Enum(SyncStatus))
    sync_version: int = Column(Integer, default=1)
    last_sync_attempt: int = Column(Integer, nullable=True)
```

### Performance Optimizations

**IndexedDB Indexes**:
```typescript
// Optimized for common query patterns
frames: {
  byMachineAndInspector: ['machineSerialNumber', 'inspectorName'],
  byStatus: 'status',
  bySyncStatus: 'syncStatus'
}

captures: {
  byFrameId: 'frameId',
  bySyncStatus: 'syncStatus',
  byFrameAndTimestamp: ['frameId', 'captureTimestamp']
}

syncQueue: {
  byStatus: 'status',
  byStatusAndPriority: ['status', 'priority'],
  byObjectReference: ['objectType', 'objectId']
}
```

**SQLite Indexes**:
```python
# Composite indexes for performance
Index('idx_captures_frame_timestamp', 'frame_id', 'capture_timestamp'),
Index('idx_captures_sync_status', 'sync_status'),
Index('idx_captures_sync_version', 'capture_id', 'sync_version'),
Index('idx_captures_frame_sync', 'frame_id', 'sync_status'),
Index('idx_captures_timestamp', 'capture_timestamp')
```

## Sync System Architecture

The sync system is built around a reactive, event-driven architecture that eliminates polling overhead and provides real-time UI updates.

### Core Components

```typescript
┌─────────────────────────────────────────────────────────────────┐
│                    Sync System Architecture                     │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Enhanced Sync State Manager                    │ │
│  │  - RxJS BehaviorSubject for reactive state                  │ │
│  │  - Granular item-level tracking                             │ │
│  │  - Frame-specific state management                          │ │
│  │  - Debounced updates (100ms) with force updates             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               Reactive Sync Service                         │ │
│  │  - Batch parallel processing (5 concurrent items)           │ │
│  │  - Intelligent error categorization & retry strategies      │ │
│  │  - Adaptive backoff delays based on error type              │ │
│  │  - Performance metrics & progress tracking                  │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Reactive Database                            │ │
│  │  - Event emission on all database changes                   │ │
│  │  - Automatic sync queue item creation                       │ │
│  │  - Optimistic UI updates with rollback capability          │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Batch Parallel Processing

The sync system processes items in parallel batches for optimal performance:

```typescript
class ReactiveSyncService {
  private readonly DEFAULT_BATCH_SIZE = 5;
  private readonly MAX_BATCH_SIZE = 10;

  async processBatchedItems(items: SyncQueueItem[]): Promise<void> {
    const batchSize = Math.min(this.DEFAULT_BATCH_SIZE, this.MAX_BATCH_SIZE);
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      // Process batch in parallel using Promise.allSettled
      const batchResults = await Promise.allSettled(
        batch.map(item => this.processSyncItemWithErrorHandling(item))
      );
      
      // Handle results and update progress
      await this.processBatchResults(batch, batchResults, progress);
    }
  }
}
```

### Error Handling & Retry Strategy

```typescript
interface ErrorHandlingStrategy {
  categorizeError(message: string): 'network' | 'auth' | 'server' | 'client' | 'unknown';
  shouldRetryBasedOnError(errorType: string, attemptCount: number): boolean;
  calculateBackoffDelay(errorType: string, attemptCount: number): number;
}

// Intelligent retry logic
const retryStrategies = {
  network: { maxRetries: 5, backoffFactor: 2, maxDelay: 60000 },
  server: { maxRetries: 4, backoffFactor: 1.5, maxDelay: 30000 },
  auth: { maxRetries: 2, backoffFactor: 1, maxDelay: 10000 },
  client: { maxRetries: 0 }, // Don't retry client errors
  unknown: { maxRetries: 3, backoffFactor: 2, maxDelay: 30000 }
};
```

### Sync State Management

```typescript
interface EnhancedSyncState {
  stats: {
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  };
  progress: SyncProgress | null;
  isProcessing: boolean;
  lastUpdated: number;
  itemUpdates: ItemUpdate[];      // Last 100 item-level changes
  frameStates: Map<string, FrameState>; // Per-frame sync status
}

// Reactive subscriptions eliminate polling
const syncState$ = enhancedSyncStateManager.getSyncState();
const frameState$ = enhancedSyncStateManager.getFrameState(frameId);
```

## AI/ML Pipeline

The system uses client-side AI inference with TensorFlow.js and YOLOv8n for real-time defect detection.

### Model Architecture

```typescript
┌─────────────────────────────────────────────────────────────────┐
│                      AI/ML Pipeline                             │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │  Camera Input   │ -> │  Preprocessing  │ -> │  YOLOv8n    │  │
│  │  (WebRTC)       │    │  (640x640)      │    │  Inference  │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
│           │                       │                       │     │
│           v                       v                       v     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │  Frame Capture  │    │  Tensor Ops     │    │  Post-proc  │  │
│  │  (Blob Storage) │    │  (WebGL/CPU)    │    │  (NMS/IoU)  │  │
│  └─────────────────┘    └─────────────────┘    └─────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### Performance Optimizations

**Model Loading & Caching**:
```typescript
class ModelLoader {
  private modelCache: Map<string, tf.GraphModel> = new Map();
  
  async loadModel(modelName: string): Promise<tf.GraphModel> {
    // Check cache first
    if (this.modelCache.has(modelName)) {
      return this.modelCache.get(modelName)!;
    }
    
    // Load with progress tracking
    const model = await tf.loadGraphModel(config.modelUrl, {
      onProgress: (fraction) => onProgress?.(fraction)
    });
    
    // Cache for reuse
    this.modelCache.set(modelName, model);
    return model;
  }
  
  // Device-optimized model selection
  getOptimalModelConfig(): { modelName: string; inputSize: number } {
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    const isLowEndDevice = navigator.hardwareConcurrency <= 2;
    
    if (isMobile || isLowEndDevice) {
      return { modelName: 'yolov8n-small', inputSize: 320 };
    }
    return { modelName: 'yolov8n', inputSize: 640 };
  }
}
```

**Inference Pipeline**:
```typescript
interface DetectionPipeline {
  preprocess(image: HTMLImageElement): { tensor: tf.Tensor; metadata: PreprocessResult };
  inference(tensor: tf.Tensor, model: tf.GraphModel): Promise<tf.Tensor>;
  postprocess(output: tf.Tensor, metadata: PreprocessResult): Promise<DetectionResult[]>;
}

// YOLOv8 output processing
const postprocessYOLOv8 = async (output: tf.Tensor): Promise<DetectionResult[]> => {
  // YOLOv8 format: [batch, 84, 8400] = 4 bbox coords + 80 class scores
  const [, numFeatures, numBoxes] = output.shape;
  
  // Apply confidence filtering and NMS
  const detections = await extractDetections(output, confidenceThreshold);
  const filteredDetections = await applyNMS(detections, iouThreshold);
  
  return filteredDetections;
};
```

### Memory Management

```typescript
class InferenceSession {
  async detect(image: HTMLImageElement): Promise<DetectionResult[]> {
    let preprocessResult: PreprocessResult | undefined;
    let inferenceResult: tf.Tensor | undefined;
    
    try {
      preprocessResult = preprocess(image, inputSize, inputSize);
      inferenceResult = model.predict(preprocessResult.tensor) as tf.Tensor;
      
      const detections = await postprocess(
        inferenceResult,
        preprocessResult,
        confidenceThreshold,
        iouThreshold
      );
      
      return detections;
    } finally {
      // Automatic tensor cleanup
      preprocessResult?.tensor.dispose();
      inferenceResult?.dispose();
    }
  }
}
```

## Authentication & Security

The system implements a comprehensive JWT-based authentication system with automatic token refresh and role-based access control.

### Authentication Flow

```typescript
┌─────────────────────────────────────────────────────────────────┐
│                   Authentication Architecture                   │
├─────────────────────────────────────────────────────────────────┤
│  Frontend                              Backend                   │
│  ┌─────────────────┐                  ┌─────────────────┐       │
│  │  Auth Context   │ <----- JWT ----> │  Auth Service   │       │
│  │  (React)        │                  │  (FastAPI)      │       │
│  └─────────────────┘                  └─────────────────┘       │
│           │                                     │                │
│           v                                     v                │
│  ┌─────────────────┐                  ┌─────────────────┐       │
│  │ Authenticated   │                  │  User Repository│       │
│  │ Fetch Service   │                  │  (SQLAlchemy)   │       │
│  └─────────────────┘                  └─────────────────┘       │
│           │                                     │                │
│           v                                     v                │
│  ┌─────────────────┐                  ┌─────────────────┐       │
│  │  Local Storage  │                  │   SQLite DB     │       │
│  │  (Tokens)       │                  │   (Users)       │       │
│  └─────────────────┘                  └─────────────────┘       │
└─────────────────────────────────────────────────────────────────┘
```

### Token Management

**Frontend Implementation**:
```typescript
class AuthenticatedFetchService {
  async authenticatedFetch(url: string, options: RequestInit): Promise<Response> {
    let tokens = this.getAuthTokens();
    
    // Automatic token refresh
    if (!skipAuthRefresh && this.isTokenExpired()) {
      const refreshSuccess = await this.ensureTokenRefresh();
      if (!refreshSuccess) {
        throw new Error('Authentication expired. Please log in again.');
      }
      tokens = this.getAuthTokens();
    }
    
    // Add auth header
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        ...options.headers,
      },
    });
    
    // Handle 401 with automatic retry
    if (response.status === 401 && !skipAuthRefresh) {
      const refreshSuccess = await this.ensureTokenRefresh();
      if (refreshSuccess) {
        // Retry with new token
        return this.authenticatedFetch(url, { ...options, skipAuthRefresh: true });
      }
    }
    
    return response;
  }
}
```

**Backend Implementation**:
```python
# JWT Configuration
SECRET_KEY = settings.secret_key
REFRESH_SECRET_KEY = os.getenv("REFRESH_SECRET_KEY", settings.secret_key)
ALGORITHM = settings.algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = settings.access_token_expire_minutes
REFRESH_TOKEN_EXPIRE_DAYS = 7

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire, "type": "access"})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def verify_token(token: str) -> dict:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        if username is None or token_type != "access":
            raise HTTPException(status_code=401, detail="Invalid token")
        return {"username": username, "payload": payload}
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
```

### Role-Based Access Control

```python
class UserRole(str, enum.Enum):
    ADMIN = "admin"
    INSPECTOR = "inspector"

# Dependency injection for role verification
async def require_admin(current_user: User = Depends(get_current_user)):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

# Protected route example
@router.get("/admin/users")
async def list_users(admin_user: User = Depends(require_admin)):
    # Admin-only functionality
    pass
```

## Offline-First Design

The system is architected around offline-first principles, ensuring full functionality without network connectivity.

### Offline-First Patterns

```typescript
┌─────────────────────────────────────────────────────────────────┐
│                    Offline-First Architecture                   │
├─────────────────────────────────────────────────────────────────┤
│  User Action                                                     │
│       │                                                          │
│       v                                                          │
│  ┌─────────────────┐                                            │
│  │  IndexedDB      │ <- Always write locally first             │
│  │  (Local Store)  │                                            │
│  └─────────────────┘                                            │
│       │                                                          │
│       v                                                          │
│  ┌─────────────────┐                                            │
│  │  Sync Queue     │ <- Queue for background sync              │
│  │  (Retry Logic)  │                                            │
│  └─────────────────┘                                            │
│       │                                                          │
│       v                                                          │
│  ┌─────────────────┐                                            │
│  │  Background     │ <- Sync when connected                     │
│  │  Sync Service   │                                            │
│  └─────────────────┘                                            │
│       │                                                          │
│       v                                                          │
│  ┌─────────────────┐                                            │
│  │  Server API     │ <- Update server state                     │
│  │  (FastAPI)      │                                            │
│  └─────────────────┘                                            │
└─────────────────────────────────────────────────────────────────┘
```

### Data Flow Strategy

**Write Operations**:
1. **Immediate Local Write**: All data writes go to IndexedDB first
2. **Optimistic UI Update**: UI reflects changes immediately
3. **Queue Sync Item**: Add operation to sync queue
4. **Background Sync**: Process queue when online
5. **Conflict Resolution**: Handle server conflicts gracefully

**Read Operations**:
1. **Local-First**: Always read from IndexedDB
2. **Background Refresh**: Sync latest data from server
3. **Delta Updates**: Only sync changed items
4. **Cache Validation**: Ensure data freshness

### Conflict Resolution

```typescript
interface ConflictResolution {
  detectConflict(local: Capture, remote: Capture): boolean;
  resolveConflict(local: Capture, remote: Capture): Capture;
  applyResolution(resolved: Capture): Promise<void>;
}

const conflictResolver = {
  // Last-write-wins with timestamp comparison
  resolveTimestampConflict(local: Capture, remote: Capture): Capture {
    return local.lastModifiedTimestamp > remote.lastModifiedTimestamp ? local : remote;
  },
  
  // Version-based optimistic concurrency
  resolveVersionConflict(local: Capture, remote: Capture): Capture {
    if (local.syncVersion !== remote.syncVersion) {
      // Increment version and merge changes
      return {
        ...remote,
        syncVersion: Math.max(local.syncVersion, remote.syncVersion) + 1
      };
    }
    return remote;
  }
};
```

## PWA Implementation

The application is built as a Progressive Web App with full offline capabilities and native app-like experience.

### Service Worker Architecture

```typescript
// Background sync registration
class PWAService {
  async triggerBackgroundSync() {
    if (this.registration?.sync) {
      await this.registration.sync.register('weld-sync');
    } else {
      // Fallback for development
      reactiveSyncService.processSyncQueue();
    }
  }
}

// Service worker event handlers
self.addEventListener('sync', event => {
  if (event.tag === 'weld-sync') {
    event.waitUntil(performBackgroundSync());
  }
});

self.addEventListener('push', event => {
  if (event.data) {
    const payload = event.data.json();
    event.waitUntil(
      self.registration.showNotification(payload.title, {
        body: payload.body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-192x192-maskable.png'
      })
    );
  }
});
```

### Caching Strategy

The service worker implements multiple caching strategies optimized for different resource types:

```typescript
const cachingStrategies = [
  // Static assets: Cache First
  {
    matcher: /\.(?:js|css|woff2)$/i,
    handler: new CacheFirst({
      cacheName: 'static-assets',
      plugins: [new ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 })]
    })
  },
  
  // Images: Stale While Revalidate
  {
    matcher: /\.(?:jpg|jpeg|png|svg|webp)$/i,
    handler: new StaleWhileRevalidate({
      cacheName: 'images',
      plugins: [new ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 2592000 })]
    })
  },
  
  // API calls: Network First
  {
    matcher: /\/api\//,
    handler: new NetworkFirst({
      cacheName: 'api-cache',
      networkTimeoutSeconds: 10,
      plugins: [new ExpirationPlugin({ maxEntries: 16, maxAgeSeconds: 86400 })]
    })
  }
];
```

### Installation & Updates

```typescript
class PWAService {
  private deferredPrompt: BeforeInstallPromptEvent | null = null;
  
  async showInstallPrompt(): Promise<boolean> {
    if (!this.deferredPrompt) return false;
    
    await this.deferredPrompt.prompt();
    const result = await this.deferredPrompt.userChoice;
    
    this.deferredPrompt = null;
    return result.outcome === 'accepted';
  }
  
  async requestPersistentStorage(): Promise<boolean> {
    if ('storage' in navigator && 'persist' in navigator.storage) {
      return await navigator.storage.persist();
    }
    return false;
  }
}
```

## API Design Patterns

The backend follows RESTful design principles with FastAPI, emphasizing consistency, type safety, and comprehensive error handling.

### API Structure

```python
# Router organization
app.include_router(auth_router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["users"])
app.include_router(frames_router, prefix="/api/v1/frames", tags=["frames"])
app.include_router(captures_router, prefix="/api/v1/captures", tags=["captures"])
app.include_router(sync_router, prefix="/api/v1/sync", tags=["sync"])
app.include_router(reports_router, prefix="/api/v1/reports", tags=["reports"])
```

### Request/Response Patterns

**Sync API Example**:
```python
@router.post("/frame", response_model=SyncResponse)
async def sync_frame(
    sync_request: SyncFrameRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    sync_service = SyncService(db)
    client_id = f"user_{current_user.user_id}"
    
    try:
        result = await sync_service.sync_frame_from_client(sync_request, client_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Frame sync failed: {str(e)}"
        )

# Multipart file upload for captures
@router.post("/capture", response_model=SyncResponse)
async def sync_capture(
    operation_type: str = Form(...),
    object_id: str = Form(...),
    capture_data: str = Form(...),
    original_image: Optional[UploadFile] = File(None),
    processed_image: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_user)
):
    # Handle FormData with image uploads
    pass
```

### Error Handling

```python
# Centralized exception handling
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# Service-level error handling
class SyncService:
    async def sync_frame_from_client(self, request: SyncFrameRequest, client_id: str):
        try:
            # Validate request
            if not request.frame_data:
                raise HTTPException(400, "Frame data is required")
            
            # Process sync
            result = await self._process_frame_sync(request, client_id)
            return SyncResponse(success=True, **result)
            
        except ValidationError as e:
            raise HTTPException(422, f"Validation error: {e}")
        except IntegrityError as e:
            raise HTTPException(409, "Data conflict detected")
        except Exception as e:
            logger.error(f"Sync error: {e}")
            raise HTTPException(500, "Internal sync error")
```

### Schema Validation

```python
# Pydantic schemas for type safety
class SyncFrameRequest(BaseModel):
    operation_type: Literal["create", "update", "delete"]
    object_type: Literal["frame"]
    object_id: str
    frame_data: Dict[str, Any]

class SyncResponse(BaseModel):
    success: bool
    message: str
    object_id: str
    object_type: str
    server_object_id: Optional[str] = None
    sync_timestamp: Optional[int] = None

# Automatic validation and serialization
@router.post("/sync/batch", response_model=SyncBatchResponse)
async def sync_batch(batch_request: SyncBatchRequest):
    # FastAPI automatically validates input and serializes output
    pass
```

## Performance Considerations

### Frontend Optimizations

**Bundle Splitting & Code Loading**:
```typescript
// Dynamic imports for code splitting
const DetectionPage = lazy(() => import('./pages/DetectionPage'));
const ModelLoader = lazy(() => import('./lib/detection/modelLoader'));

// Preload critical resources
const preloadModel = () => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = '/models/yolov8n_web_model/model.json';
  link.as = 'fetch';
  document.head.appendChild(link);
};
```

**Memory Management**:
```typescript
// Tensor cleanup in AI pipeline
class DetectionSession {
  private activeTensors: tf.Tensor[] = [];
  
  async runInference(image: HTMLImageElement): Promise<DetectionResult[]> {
    const tensor = tf.browser.fromPixels(image);
    this.activeTensors.push(tensor);
    
    try {
      const results = await this.model.predict(tensor);
      return await this.postprocess(results);
    } finally {
      // Automatic cleanup
      this.cleanup();
    }
  }
  
  private cleanup(): void {
    this.activeTensors.forEach(tensor => tensor.dispose());
    this.activeTensors = [];
  }
}
```

**IndexedDB Optimizations**:
```typescript
// Batch operations to reduce transaction overhead
class BatchedDBOperations {
  async batchInsertCaptures(captures: Capture[]): Promise<void> {
    const transaction = await getTransaction('captures', 'readwrite');
    const store = transaction.objectStore('captures');
    
    // Process in chunks to avoid blocking
    const CHUNK_SIZE = 50;
    for (let i = 0; i < captures.length; i += CHUNK_SIZE) {
      const chunk = captures.slice(i, i + CHUNK_SIZE);
      await Promise.all(chunk.map(capture => store.add(capture)));
    }
  }
}
```

### Backend Optimizations

**Database Performance**:
```python
# Async database operations
class CaptureService:
    async def get_captures_by_frame_batch(self, frame_ids: List[str]) -> List[Capture]:
        # Use IN clause for batch queries
        query = select(Capture).where(Capture.frame_id.in_(frame_ids))
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_sync_status_batch(self, capture_ids: List[str], status: str):
        # Batch updates
        stmt = update(Capture).where(
            Capture.capture_id.in_(capture_ids)
        ).values(sync_status=status)
        await self.db.execute(stmt)
```

**Connection Pooling**:
```python
# SQLAlchemy async engine configuration
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### Sync Performance

**Parallel Processing**:
```typescript
// Concurrent sync operations
class ParallelSyncProcessor {
  async processConcurrentBatches(items: SyncQueueItem[]): Promise<void> {
    const BATCH_SIZE = 5;
    const batches = chunk(items, BATCH_SIZE);
    
    for (const batch of batches) {
      // Process each batch in parallel
      const promises = batch.map(item => this.processSyncItem(item));
      const results = await Promise.allSettled(promises);
      
      // Handle results without blocking
      await this.processBatchResults(batch, results);
    }
  }
}
```

## Technology Choices & Trade-offs

### Frontend Stack

**Next.js 15 + React 19**:
- ✅ **Pros**: App Router, Server Components, excellent performance
- ✅ **Pros**: Built-in PWA support, great developer experience
- ⚠️ **Trade-offs**: Learning curve for App Router patterns
- ⚠️ **Trade-offs**: Bundle size vs. feature richness

**TensorFlow.js + YOLOv8n**:
- ✅ **Pros**: Client-side inference, no server dependency for AI
- ✅ **Pros**: Real-time performance, privacy-first design
- ⚠️ **Trade-offs**: Model size (~6MB), initial load time
- ⚠️ **Trade-offs**: Limited to pre-trained models

**IndexedDB + Dexie.js**:
- ✅ **Pros**: Large storage capacity, structured queries
- ✅ **Pros**: Excellent offline support, transaction support
- ⚠️ **Trade-offs**: Browser-specific limitations, complexity

### Backend Stack

**FastAPI + SQLAlchemy**:
- ✅ **Pros**: Automatic API docs, excellent type safety
- ✅ **Pros**: High performance, async support
- ⚠️ **Trade-offs**: Python ecosystem dependency
- ⚠️ **Trade-offs**: Learning curve for async patterns

**SQLite**:
- ✅ **Pros**: Zero configuration, excellent performance for read-heavy workloads
- ✅ **Pros**: Strong consistency, ACID compliance
- ⚠️ **Trade-offs**: Single-writer limitation, size constraints at scale
- ⚠️ **Trade-offs**: Limited horizontal scaling options

### Architectural Decisions

**Offline-First**:
- ✅ **Pros**: Works in industrial environments with poor connectivity
- ✅ **Pros**: Excellent user experience, no waiting for network
- ⚠️ **Trade-offs**: Complex sync logic, conflict resolution needed
- ⚠️ **Trade-offs**: Data consistency challenges

**Client-Side AI**:
- ✅ **Pros**: Real-time inference, privacy preservation
- ✅ **Pros**: Reduced server costs, edge computing benefits
- ⚠️ **Trade-offs**: Device performance dependency
- ⚠️ **Trade-offs**: Model update complexity

## Scalability Strategy

### Horizontal Scaling Considerations

**Database Evolution Path**:
```
Current: SQLite (Single Node)
    ↓
Phase 1: PostgreSQL (Single Instance)
    ↓
Phase 2: PostgreSQL + Read Replicas
    ↓
Phase 3: Sharded PostgreSQL or MongoDB
```

**Backend Scaling**:
```python
# Microservice decomposition strategy
services = {
    "auth-service": "User authentication and authorization",
    "sync-service": "Data synchronization and conflict resolution", 
    "ai-service": "Server-side AI inference and model management",
    "file-service": "Image storage and processing",
    "analytics-service": "Reporting and data analytics"
}
```

**Cache Layer Integration**:
```python
# Redis for session management and caching
@app.middleware("http")
async def add_redis_session(request: Request, call_next):
    # Session management with Redis
    session_id = request.cookies.get("session_id")
    if session_id:
        session_data = await redis.get(f"session:{session_id}")
        request.state.session = json.loads(session_data) if session_data else {}
    response = await call_next(request)
    return response
```

### Performance Monitoring

**Metrics Collection**:
```typescript
// Client-side performance monitoring
class PerformanceMonitor {
  trackSyncPerformance(operation: string, duration: number, success: boolean): void {
    const metric = {
      operation,
      duration,
      success,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      connectionType: navigator.connection?.effectiveType
    };
    
    // Send to analytics service
    this.sendMetric(metric);
  }
  
  trackAIInferencePerformance(metrics: InferenceMetrics): void {
    const performanceData = {
      preprocessTime: metrics.preprocessTime,
      inferenceTime: metrics.inferenceTime,
      postprocessTime: metrics.postprocessTime,
      totalTime: metrics.totalTime,
      modelName: metrics.modelName,
      inputSize: metrics.inputSize
    };
    
    this.sendMetric(performanceData);
  }
}
```

## Future Evolution

### Planned Enhancements

**AI/ML Improvements**:
- Custom model training pipeline for weld-specific defects
- Model versioning and A/B testing framework
- Edge deployment with WebAssembly optimization
- Multi-model ensemble for improved accuracy

**Sync System Evolution**:
- Real-time bidirectional sync with WebSockets
- Operational transformation for concurrent editing
- Delta sync algorithms for bandwidth optimization
- Distributed conflict resolution with CRDT

**Architecture Modernization**:
- Microservice decomposition with event sourcing
- GraphQL API layer for flexible data fetching
- Container orchestration with Kubernetes
- Observability stack with distributed tracing

**Security Enhancements**:
- End-to-end encryption for sensitive data
- Biometric authentication integration
- Advanced audit logging and compliance
- Zero-trust security model implementation

### Migration Strategy

**Phase 1: Foundation (Current)**:
- ✅ Offline-first architecture
- ✅ Client-side AI inference
- ✅ Reactive sync system
- ✅ PWA implementation

**Phase 2: Scale (6-12 months)**:
- 🔄 PostgreSQL migration
- 🔄 Redis caching layer
- 🔄 Container deployment
- 🔄 Performance monitoring

**Phase 3: Enterprise (12-24 months)**:
- 📋 Microservice architecture
- 📋 Real-time collaboration
- 📋 Advanced analytics
- 📋 Multi-tenant support

**Phase 4: AI-First (24+ months)**:
- 📋 Custom model training
- 📋 AutoML pipeline
- 📋 Edge AI deployment
- 📋 Predictive analytics

---

## Conclusion

The Weld Defect Detection System represents a modern, production-ready architecture that successfully balances performance, reliability, and user experience. The offline-first design ensures industrial applicability, while the reactive sync system provides enterprise-grade data consistency. The client-side AI approach delivers real-time inference without server dependencies, making the system resilient and cost-effective.

Key architectural strengths:
- **Offline-First**: Works reliably in industrial environments
- **Reactive Design**: Real-time UI updates without polling overhead
- **Performance Optimized**: Batch processing, intelligent caching, memory management
- **Production Ready**: Comprehensive error handling, retry logic, monitoring
- **Scalable Foundation**: Clear evolution path for future growth

This architecture provides a solid foundation for current requirements while maintaining flexibility for future enhancements and scale.