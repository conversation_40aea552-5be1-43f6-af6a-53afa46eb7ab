# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
backend.log

# Database
*.db
*.sqlite
*.sqlite3
weld_detection.db

# Environment files
.env
.env.local
.env.development

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/
*.md
README.md

# Git
.git/
.gitignore

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Development tools
.mypy_cache/
.bandit
.flake8

# Backup files
*.bak
*.backup