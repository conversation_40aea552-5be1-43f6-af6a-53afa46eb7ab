// Image processing utilities for upload functionality
export interface ImageDimensions {
  width: number;
  height: number;
}

export interface ProcessedImageResult {
  canvas: HTMLCanvasElement;
  blob: Blob;
  dimensions: ImageDimensions;
}

export class ImageProcessingError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'ImageProcessingError';
  }
}

// Convert File to HTMLCanvasElement
export async function fileToCanvas(file: File): Promise<HTMLCanvasElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new ImageProcessingError('Could not create canvas context', 'CANVAS_ERROR'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      // Clean up
      URL.revokeObjectURL(img.src);
      resolve(canvas);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new ImageProcessingError(`Failed to load image: ${file.name}`, 'IMAGE_LOAD_ERROR'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}

// Resize image to square format (center crop)
export function cropToSquare(sourceCanvas: HTMLCanvasElement): HTMLCanvasElement {
  const { width, height } = sourceCanvas;
  const size = Math.min(width, height);
  
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new ImageProcessingError('Could not create canvas context for cropping', 'CANVAS_ERROR');
  }
  
  canvas.width = size;
  canvas.height = size;
  
  // Calculate center crop offsets
  const xOffset = (width - size) / 2;
  const yOffset = (height - size) / 2;
  
  // Draw center-cropped image
  ctx.drawImage(
    sourceCanvas,
    xOffset, yOffset, size, size, // Source rectangle
    0, 0, size, size              // Destination rectangle
  );
  
  return canvas;
}

// Resize canvas to specific dimensions
export function resizeCanvas(sourceCanvas: HTMLCanvasElement, targetWidth: number, targetHeight: number): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new ImageProcessingError('Could not create canvas context for resizing', 'CANVAS_ERROR');
  }
  
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  
  // Use high-quality scaling
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  ctx.drawImage(sourceCanvas, 0, 0, targetWidth, targetHeight);
  
  return canvas;
}

// Convert canvas to blob with quality settings
export function canvasToBlob(canvas: HTMLCanvasElement, quality: number = 0.9): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new ImageProcessingError('Failed to convert canvas to blob', 'BLOB_ERROR'));
        }
      },
      'image/jpeg',
      quality
    );
  });
}

// Create thumbnail from canvas
export async function createThumbnail(canvas: HTMLCanvasElement, maxSize: number = 150): Promise<Blob> {
  const { width, height } = canvas;
  const aspectRatio = width / height;
  
  let thumbnailWidth = maxSize;
  let thumbnailHeight = maxSize;
  
  if (aspectRatio > 1) {
    thumbnailHeight = thumbnailWidth / aspectRatio;
  } else {
    thumbnailWidth = thumbnailHeight * aspectRatio;
  }
  
  const thumbnailCanvas = resizeCanvas(canvas, thumbnailWidth, thumbnailHeight);
  return canvasToBlob(thumbnailCanvas, 0.7);
}

// Process uploaded image for detection (standard 640x640)
export async function processImageForDetection(file: File): Promise<{
  originalCanvas: HTMLCanvasElement;
  detectionCanvas: HTMLCanvasElement;
  originalBlob: Blob;
  thumbnailBlob: Blob;
}> {
  try {
    // Step 1: Convert file to canvas
    const originalCanvas = await fileToCanvas(file);
    
    // Step 2: Crop to square
    const squareCanvas = cropToSquare(originalCanvas);
    
    // Step 3: Resize to 640x640 for detection
    const detectionCanvas = resizeCanvas(squareCanvas, 640, 640);
    
    // Step 4: Create blobs
    const originalBlob = await canvasToBlob(squareCanvas, 0.9);
    const thumbnailBlob = await createThumbnail(squareCanvas);
    
    return {
      originalCanvas: squareCanvas,
      detectionCanvas,
      originalBlob,
      thumbnailBlob
    };
  } catch (error) {
    if (error instanceof ImageProcessingError) {
      throw error;
    }
    throw new ImageProcessingError(
      `Failed to process image ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'PROCESSING_ERROR'
    );
  }
}

// Validate image file
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (!validTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Unsupported file type. Please use JPG, PNG, or WebP.'
    };
  }
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File too large. Maximum size is 10MB.'
    };
  }
  
  return { isValid: true };
}

// Get image dimensions from file without full processing
export async function getImageDimensions(file: File): Promise<ImageDimensions> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
      URL.revokeObjectURL(img.src);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new ImageProcessingError(`Failed to load image dimensions: ${file.name}`, 'DIMENSIONS_ERROR'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}