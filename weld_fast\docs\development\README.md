# Developer Guide

Welcome to the Weld Defect Detection System development guide. This document provides comprehensive instructions for setting up your development environment and contributing to the project.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Project Architecture](#project-architecture)
- [Development Environment Setup](#development-environment-setup)
- [Code Structure](#code-structure)
- [Development Workflow](#development-workflow)
- [Testing](#testing)
- [Code Style & Linting](#code-style--linting)
- [Debugging](#debugging)
- [Performance Optimization](#performance-optimization)
- [Common Issues](#common-issues)
- [Contributing](#contributing)

## Prerequisites

### Required Software

- **Node.js**: v18.0.0 or higher (LTS recommended)
- **Python**: v3.12 or higher
- **uv**: Python package manager (install from [astral-sh/uv](https://github.com/astral-sh/uv))
- **Git**: Latest stable version

### System Requirements

- **RAM**: Minimum 8GB (16GB recommended for AI model processing)
- **Storage**: At least 2GB free space
- **OS**: Windows 10+, macOS 11+, or Linux (Ubuntu 20.04+ recommended)
- **Browser**: Chrome/Edge 90+ or Firefox 88+ (for WebRTC and WebGL support)

### Development Tools (Recommended)

- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - Python extension
  - ESLint
  - Tailwind CSS IntelliSense
  - Prettier

## Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd weld_fast
```

### 2. Backend Setup

```bash
cd backend

# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies
uv sync

# Run development server
uv run uvicorn app.main:app --reload
```

The backend will be available at `http://localhost:8000`

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev
```

The frontend will be available at `http://localhost:3000`

### 4. Verify Setup

1. Check backend health: `http://localhost:8000/docs` (FastAPI docs)
2. Check frontend: `http://localhost:3000`
3. Run backend tests: `cd backend && uv run python test_api.py`

## Project Architecture

### Overview

This is an **offline-first weld defect detection system** with the following architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (SQLite)      │
│                 │    │                 │    │                 │
│ • React 19      │    │ • SQLAlchemy    │    │ • Server DB     │
│ • TypeScript    │    │ • JWT Auth      │    │ • IndexedDB     │
│ • TensorFlow.js │    │ • Sync Service  │    │   (Client)      │
│ • Service Worker│    │ • File Storage  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Technologies

**Frontend:**
- **Next.js 15** with App Router and React 19
- **TypeScript** for type safety
- **Tailwind CSS v4** for styling
- **shadcn/ui** for UI components
- **TensorFlow.js** with YOLOv8n for AI inference
- **IndexedDB** for offline-first storage
- **Service Worker** for PWA capabilities

**Backend:**
- **FastAPI** with automatic OpenAPI documentation
- **SQLAlchemy** ORM with async support
- **SQLite** database with aiosqlite
- **JWT Authentication** with bcrypt
- **PIL** for image processing
- **ReportLab** for PDF generation

### Key Features

- **Offline-First**: All operations work without internet connection
- **Real-Time AI**: Client-side YOLOv8n inference for weld defect detection
- **Sync System**: Intelligent background synchronization with conflict resolution
- **PWA Support**: Installable web app with service worker
- **Authentication**: JWT-based auth with role-based access
- **Batch Processing**: Parallel processing for improved performance

## Development Environment Setup

### Backend Development

#### 1. Python Environment

```bash
cd backend

# Verify Python version
python --version  # Should be 3.12+

# Install uv package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
# or on Windows: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Install dependencies
uv sync

# Verify installation
uv run python --version
```

#### 2. Database Setup

```bash
# Database migrations (if any)
uv run python -m app.migrations

# The SQLite database will be created automatically on first run
```

#### 3. Environment Variables

Create `.env` file in the backend directory:

```env
# Backend Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=sqlite:///./weld_detection.db

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB

# Development
DEBUG=True
```

#### 4. Running Backend

```bash
# Development server with auto-reload
uv run uvicorn app.main:app --reload

# Alternative entry point
uv run uvicorn main:app --reload

# Production server
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Frontend Development

#### 1. Node.js Setup

```bash
cd frontend

# Verify Node.js version
node --version  # Should be 18.0.0+
npm --version

# Install dependencies
npm install

# Verify installation
npm list --depth=0
```

#### 2. Environment Variables

Create `.env.local` file in the frontend directory:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api/v1

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# PWA Configuration
NEXT_PUBLIC_PWA_ENABLED=true
```

#### 3. Running Frontend

```bash
# Development server with Turbopack
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Lint code
npm run lint
```

### Docker Development (Optional)

#### Backend Docker

```bash
cd backend

# Build image
docker build -t weld-detection-backend .

# Run container
docker run -p 8000:8000 weld-detection-backend
```

#### Full Stack with Docker Compose

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=True
  
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
```

## Code Structure

### Frontend Structure

```
frontend/src/
├── app/                    # Next.js App Router
│   ├── page.tsx           # Home page (session creation)
│   ├── detection/         # Detection interface
│   ├── login/             # Authentication pages
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── detection/         # Detection-specific components
│   ├── auth/              # Authentication components
│   ├── ui/                # shadcn/ui components
│   └── layout/            # Layout components
├── context/               # React Context providers
│   ├── SessionContext.tsx # Global session state
│   └── AuthContext.tsx    # Authentication state
├── hooks/                 # Custom React hooks
│   ├── useAutoRefresh.ts  # Auto-refresh functionality
│   └── useSyncEvents.ts   # Sync state management
├── lib/                   # Utility libraries
│   ├── db/                # IndexedDB operations
│   ├── detection/         # AI/ML detection logic
│   ├── sync/              # Synchronization system
│   └── auth/              # Authentication utilities
└── workers/               # Web Workers and Service Workers
```

### Backend Structure

```
backend/
├── app/
│   ├── main.py            # FastAPI application entry
│   ├── config.py          # Configuration management
│   ├── database.py        # Database connection
│   ├── auth.py            # Authentication logic
│   ├── routers/           # API route handlers
│   │   ├── auth.py        # Authentication endpoints
│   │   ├── frames.py      # Frame management
│   │   ├── captures.py    # Capture operations
│   │   └── sync.py        # Synchronization endpoints
│   ├── services/          # Business logic
│   │   ├── sync_service.py      # Sync operations
│   │   ├── file_storage_service.py # File handling
│   │   └── analytics_service.py # Analytics
│   ├── models/            # SQLAlchemy models
│   ├── schemas/           # Pydantic schemas
│   └── utils/             # Utility functions
├── tests/                 # Test files
└── pyproject.toml         # Dependencies and config
```

### Key Components Deep Dive

#### Frontend Components

**SessionContext** (`frontend/src/context/SessionContext.tsx`):
- Manages global session state
- Handles session persistence in IndexedDB
- Provides session data to all components

**Detection Interface** (`frontend/src/app/detection/page.tsx`):
- Main detection interface
- Integrates camera, AI processing, and result display
- Manages capture history and sync status

**Sync System** (`frontend/src/lib/sync/`):
- `enhancedSyncStateManager.ts`: Global sync state management
- `reactiveSyncService.ts`: Background synchronization
- Handles offline-first operations with intelligent retry

#### Backend Services

**Sync Service** (`backend/app/services/sync_service.py`):
- Handles client-server synchronization
- Manages conflict resolution
- Provides batch operations for performance

**Authentication** (`backend/app/auth.py`):
- JWT token management
- Password hashing with bcrypt
- Role-based access control

## Development Workflow

### 1. Starting Development

```bash
# Terminal 1: Backend
cd backend
uv run uvicorn app.main:app --reload

# Terminal 2: Frontend
cd frontend
npm run dev

# Terminal 3: Testing (optional)
cd backend
uv run python test_api.py
```

### 2. Feature Development Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Follow Offline-First Pattern**
   - All data operations go through IndexedDB first
   - UI updates optimistically
   - Background sync handles server communication

3. **Component Development**
   - Use shadcn/ui components for consistency
   - Follow TypeScript strict mode
   - Implement proper error boundaries

4. **API Development**
   - Use FastAPI dependency injection
   - Follow RESTful conventions
   - Implement proper error handling

5. **Testing**
   ```bash
   # Frontend linting
   cd frontend && npm run lint
   
   # Backend testing
   cd backend && uv run python test_api.py
   ```

### 3. Database Operations

#### Frontend (IndexedDB)

```typescript
// Example: Creating a new frame
import { frameOperations } from '@/lib/db/frameOperations';

const newFrame = await frameOperations.create({
  modelNumber: 'WM-2024',
  machineSerialNumber: 'SN123456',
  inspectorName: 'John Doe'
});
```

#### Backend (SQLAlchemy)

```python
# Example: Creating a new frame
from app.models.database import Frame
from app.database import get_db

async def create_frame(frame_data: FrameCreate, db: Session):
    db_frame = Frame(**frame_data.dict())
    db.add(db_frame)
    db.commit()
    return db_frame
```

### 4. Authentication Flow

```typescript
// Frontend: Login
import { authAPI } from '@/lib/api/auth';

const { token, user } = await authAPI.login(username, password);
localStorage.setItem('token', token);
```

```python
# Backend: Token verification
from app.auth import verify_token

@router.get("/protected")
async def protected_route(current_user: User = Depends(get_current_user)):
    return {"user": current_user}
```

## Testing

### Frontend Testing

Currently, the frontend uses **ESLint** for code quality:

```bash
cd frontend

# Run linting
npm run lint

# Fix auto-fixable issues
npm run lint -- --fix
```

**Future Testing Setup** (recommended):
- Jest for unit testing
- React Testing Library for component testing
- Playwright for E2E testing

### Backend Testing

Manual integration testing is currently implemented:

```bash
cd backend

# Start the server first
uv run uvicorn app.main:app --reload

# In another terminal, run tests
uv run python test_api.py
uv run python test_sync.py
```

**Test Structure**:
- `test_api.py`: Basic API endpoint testing
- `test_sync.py`: Synchronization testing
- `tests/`: Pytest-based test suite (partial)

**Running Specific Tests**:
```bash
# Run pytest tests
uv run pytest tests/

# Run specific test file
uv run pytest tests/test_capture_api.py

# Run with verbose output
uv run pytest -v tests/
```

### Database Testing

```bash
# Test database operations
uv run python -c "
from app.database import engine, Base
Base.metadata.create_all(bind=engine)
print('Database tables created successfully')
"
```

## Code Style & Linting

### Frontend Standards

**TypeScript Configuration**:
- Strict mode enabled
- ES2017 target with modern libraries
- Path aliases configured (`@/*` → `./src/*`)

**ESLint Rules**:
- Next.js recommended rules
- TypeScript integration
- Core Web Vitals rules

**Code Style**:
```typescript
// Good: Use proper typing
interface SessionData {
  frameId: string;
  modelNumber: string;
  inspectorName: string;
}

// Good: Use async/await
const createSession = async (data: SessionData): Promise<void> => {
  try {
    await frameOperations.create(data);
  } catch (error) {
    console.error('Failed to create session:', error);
  }
};

// Good: Use proper error handling
const handleCameraError = (error: Error) => {
  if (error.name === 'NotAllowedError') {
    showError('Camera access denied');
  } else {
    showError('Camera error occurred');
  }
};
```

### Backend Standards

**Python Style**:
- Follow PEP 8
- Use type hints
- Async/await for I/O operations
- Proper exception handling

```python
# Good: Proper typing and async
async def create_frame(
    frame_data: FrameCreate,
    db: AsyncSession = Depends(get_db)
) -> Frame:
    try:
        db_frame = Frame(**frame_data.dict())
        db.add(db_frame)
        await db.commit()
        return db_frame
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# Good: Proper error handling
@router.post("/frames/")
async def create_frame_endpoint(
    frame: FrameCreate,
    current_user: User = Depends(get_current_user)
):
    if not current_user.is_inspector:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    return await frame_service.create_frame(frame, current_user.id)
```

## Debugging

### Frontend Debugging

#### Browser DevTools

1. **Network Tab**: Monitor API calls and sync operations
2. **Application Tab**: 
   - IndexedDB inspection
   - Service Worker debugging
   - Local Storage inspection
3. **Console**: Check for errors and custom logs

#### Debug Components

```typescript
// Enable debug mode
const DEBUG = process.env.NEXT_PUBLIC_DEBUG === 'true';

// Debug logging
if (DEBUG) {
  console.log('Sync state:', syncState);
  console.log('IndexedDB data:', await db.getAllFrames());
}
```

#### Sync Debugging

```typescript
// Monitor sync events
import { syncStateManager } from '@/lib/sync/enhancedSyncStateManager';

syncStateManager.subscribe(state => {
  console.log('Sync state changed:', state);
});
```

### Backend Debugging

#### FastAPI Debug Mode

```python
# app/main.py
app = FastAPI(debug=True)  # Enable debug mode

# Or via environment
import os
DEBUG = os.getenv("DEBUG", "false").lower() == "true"
```

#### Debugging Tools

```bash
# Enable verbose logging
uv run uvicorn app.main:app --reload --log-level debug

# Database debugging
uv run python -c "
from app.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT * FROM frames LIMIT 5'))
    print(list(result))
"
```

#### API Documentation

FastAPI provides automatic API documentation:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Common Debug Scenarios

#### Sync Issues

```typescript
// Check sync queue status
import { syncQueueOperations } from '@/lib/db/syncQueueOperations';

const queueItems = await syncQueueOperations.getAll();
console.log('Pending sync items:', queueItems);
```

#### Camera Issues

```typescript
// Debug camera constraints
navigator.mediaDevices.getUserMedia({
  video: {
    width: { ideal: 1920 },
    height: { ideal: 1080 },
    facingMode: 'user'
  }
}).then(stream => {
  const track = stream.getVideoTracks()[0];
  console.log('Camera capabilities:', track.getCapabilities());
  console.log('Camera settings:', track.getSettings());
});
```

## Performance Optimization

### Frontend Optimization

#### 1. Bundle Analysis

```bash
# Analyze bundle size
npm run build
npx @next/bundle-analyzer
```

#### 2. Image Optimization

```typescript
// Optimize images before storage
import { optimizeImage } from '@/lib/upload/imageProcessing';

const optimizedBlob = await optimizeImage(originalBlob, {
  maxWidth: 1920,
  maxHeight: 1080,
  quality: 0.8
});
```

#### 3. IndexedDB Performance

```typescript
// Use transactions for batch operations
const tx = db.transaction(['frames', 'captures'], 'readwrite');
const frameStore = tx.objectStore('frames');
const captureStore = tx.objectStore('captures');

// Batch insert
for (const frame of frames) {
  frameStore.add(frame);
}
await tx.complete;
```

#### 4. AI Model Optimization

```typescript
// Warm up TensorFlow.js
import { loadModel } from '@/lib/detection/modelLoader';

// Preload model on app start
const model = await loadModel();
console.log('Model loaded and ready');
```

### Backend Optimization

#### 1. Database Optimization

```python
# Use database indexes
class Frame(Base):
    __tablename__ = "frames"
    
    id = Column(String, primary_key=True, index=True)
    inspector_id = Column(String, index=True)  # Indexed for fast lookups
    created_at = Column(DateTime, index=True)  # Indexed for sorting
```

#### 2. Async Operations

```python
# Use async for I/O operations
async def batch_sync_frames(frame_ids: List[str], db: AsyncSession):
    tasks = [sync_single_frame(frame_id, db) for frame_id in frame_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

#### 3. File Processing

```python
# Optimize image processing
from PIL import Image
import asyncio

async def process_image(image_data: bytes) -> bytes:
    def _process():
        image = Image.open(io.BytesIO(image_data))
        image.thumbnail((1920, 1080), Image.Resampling.LANCZOS)
        
        output = io.BytesIO()
        image.save(output, format='JPEG', quality=85, optimize=True)
        return output.getvalue()
    
    return await asyncio.get_event_loop().run_in_executor(None, _process)
```

### Performance Monitoring

#### Frontend Metrics

```typescript
// Monitor sync performance
const syncStart = performance.now();
await syncManager.syncAll();
const syncDuration = performance.now() - syncStart;
console.log(`Sync completed in ${syncDuration}ms`);

// Monitor IndexedDB operations
const dbStart = performance.now();
const frames = await frameOperations.getAll();
const dbDuration = performance.now() - dbStart;
console.log(`DB query took ${dbDuration}ms`);
```

#### Backend Metrics

```python
# Monitor API response times
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

## Common Issues

### Installation Issues

#### Node.js Version Conflicts

```bash
# Use Node Version Manager (nvm)
nvm install 18
nvm use 18

# Or verify version
node --version  # Should be 18.0.0+
```

#### Python Version Issues

```bash
# Check Python version
python --version  # Should be 3.12+

# Install specific Python version (Ubuntu/Debian)
sudo apt update
sudo apt install python3.12 python3.12-venv

# Or use pyenv
pyenv install 3.12.0
pyenv local 3.12.0
```

#### uv Installation Issues

```bash
# Alternative installation methods
pip install uv

# Or on macOS
brew install uv

# Verify installation
uv --version
```

### Runtime Issues

#### Camera Access Denied

```typescript
// Handle camera permissions
const handleCameraAccess = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
    return stream;
  } catch (error) {
    if (error.name === 'NotAllowedError') {
      alert('Camera access is required for defect detection');
    } else if (error.name === 'NotFoundError') {
      alert('No camera found on this device');
    }
    throw error;
  }
};
```

#### IndexedDB Quota Exceeded

```typescript
// Monitor storage usage
const estimate = await navigator.storage.estimate();
const usedMB = (estimate.usage / 1024 / 1024).toFixed(2);
const quotaMB = (estimate.quota / 1024 / 1024).toFixed(2);
console.log(`Storage: ${usedMB}MB / ${quotaMB}MB`);

// Clean up old data
if (estimate.usage > estimate.quota * 0.8) {
  await cleanupOldCaptures();
}
```

#### Backend Database Locks

```python
# Handle SQLite database locks
import sqlite3
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_db_with_retry(max_retries=3):
    for attempt in range(max_retries):
        try:
            async with get_db() as db:
                yield db
                break
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and attempt < max_retries - 1:
                await asyncio.sleep(0.1 * (2 ** attempt))  # Exponential backoff
                continue
            raise
```

### Sync Issues

#### Conflict Resolution

```typescript
// Handle sync conflicts
const resolveConflict = async (localItem: Frame, serverItem: Frame) => {
  // Use timestamp-based resolution
  if (localItem.lastModified > serverItem.lastModified) {
    return await syncManager.pushToServer(localItem);
  } else {
    return await syncManager.pullFromServer(serverItem);
  }
};
```

#### Network Connectivity

```typescript
// Monitor network status
window.addEventListener('online', () => {
  console.log('Back online, resuming sync');
  syncManager.resumeSync();
});

window.addEventListener('offline', () => {
  console.log('Gone offline, pausing sync');
  syncManager.pauseSync();
});
```

### Performance Issues

#### Slow AI Inference

```typescript
// Optimize TensorFlow.js performance
import '@tensorflow/tfjs-backend-webgl';

// Enable GPU acceleration
await tf.setBackend('webgl');
await tf.ready();

// Use smaller input sizes
const inputTensor = tf.browser.fromPixels(canvas)
  .resizeNearestNeighbor([416, 416])  // Smaller than 640x640
  .expandDims(0);
```

#### Memory Leaks

```typescript
// Dispose of tensors
const prediction = model.predict(inputTensor);
inputTensor.dispose();  // Important!

// Monitor memory usage
console.log('Memory info:', tf.memory());
```

## Contributing

### Git Workflow

1. **Fork and Clone**
   ```bash
   git fork <repository-url>
   git clone <your-fork-url>
   cd weld_fast
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Follow code style guidelines
   - Add tests for new functionality
   - Update documentation if needed

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create PR on GitHub
   ```

### Commit Message Convention

Use conventional commits:
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test changes
- `chore:` Build/maintenance tasks

### Code Review Process

1. **Self Review**: Test your changes locally
2. **Lint Check**: Run `npm run lint` and fix issues
3. **Manual Testing**: Run integration tests
4. **Documentation**: Update relevant documentation
5. **Submit PR**: Create detailed pull request

### Development Environment Tips

1. **Use VS Code Extensions**:
   - ESLint for code quality
   - Prettier for formatting
   - Python extension for backend
   - REST Client for API testing

2. **Keep Dependencies Updated**:
   ```bash
   # Frontend
   npm audit
   npm update
   
   # Backend
   uv sync --upgrade
   ```

3. **Regular Database Cleanup**:
   ```bash
   # Clear development database
   rm backend/weld_detection.db
   ```

4. **Monitor Performance**:
   - Use browser DevTools Performance tab
   - Monitor IndexedDB storage usage
   - Check API response times

### Getting Help

- **Documentation**: Check existing docs in `/docs/`
- **API Reference**: `http://localhost:8000/docs`
- **Code Examples**: Look at existing components and services
- **Issues**: Create GitHub issues for bugs or feature requests

---

This guide should get you up and running with the Weld Defect Detection System. For specific questions or issues, please refer to the relevant sections or create an issue in the repository.