from fastapi import APIRouter, Depends, HTTPException, status, Form, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import time
import json

from ..database import get_db
from ..services.sync_service import SyncService
from ..schemas.sync import (
    SyncFrameRequest, SyncCaptureRequest, SyncResponse,
    SyncBatchRequest, SyncBatchResponse, SyncErrorDetail
)
from ..dependencies import get_current_user
from ..models.database import User

router = APIRouter(prefix="/api/v1/sync", tags=["sync"])


@router.post("/frame", response_model=SyncResponse)
async def sync_frame(
    sync_request: SyncFrameRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Sync a single frame from client to server.
    Handles create, update, and delete operations.
    """
    sync_service = SyncService(db)
    client_id = f"user_{current_user.user_id}"
    
    try:
        result = await sync_service.sync_frame_from_client(sync_request, client_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Frame sync failed: {str(e)}"
        )


@router.post("/capture", response_model=SyncResponse)  
async def sync_capture(
    operation_type: str = Form(...),
    object_type: str = Form(...),
    object_id: str = Form(...),
    frame_id: str = Form(...),
    capture_data: str = Form(...),
    original_image: Optional[UploadFile] = File(None),
    processed_image: Optional[UploadFile] = File(None),
    thumbnail_image: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Sync a single capture from client to server with image upload support.
    Handles create, update, and delete operations.
    """
    sync_service = SyncService(db)
    client_id = f"user_{current_user.user_id}"
    
    try:
        # Parse the JSON capture data
        try:
            capture_data_dict = json.loads(capture_data)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid capture_data JSON format"
            )
        
        # Read image files if provided
        image_blobs = {}
        if original_image:
            image_blobs['originalImageBlob'] = await original_image.read()
        if processed_image:
            image_blobs['processedImageBlob'] = await processed_image.read()
        if thumbnail_image:
            image_blobs['thumbnailBlob'] = await thumbnail_image.read()
        
        # Add image blobs to capture data
        capture_data_dict.update(image_blobs)
        
        # Create sync request object
        sync_request = SyncCaptureRequest(
            operation_type=operation_type,
            object_type=object_type,
            object_id=object_id,
            frame_id=frame_id,
            capture_data=capture_data_dict
        )
        
        result = await sync_service.sync_capture_from_client(sync_request, client_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Capture sync failed: {str(e)}"
        )


@router.post("/batch", response_model=SyncBatchResponse)
async def sync_batch(
    batch_request: SyncBatchRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Sync multiple items in a single batch request.
    Processes each item individually and returns results for all.
    """
    sync_service = SyncService(db)
    client_id = f"user_{current_user.user_id}"
    
    results: List[SyncResponse] = []
    errors: List[SyncErrorDetail] = []
    successful = 0
    failed = 0
    
    for request_item in batch_request.requests:
        try:
            if isinstance(request_item, SyncFrameRequest):
                result = await sync_service.sync_frame_from_client(request_item, client_id)
            elif isinstance(request_item, SyncCaptureRequest):
                result = await sync_service.sync_capture_from_client(request_item, client_id)
            else:
                result = SyncResponse(
                    success=False,
                    message="Unknown request type",
                    object_id=getattr(request_item, 'object_id', 'unknown'),
                    object_type=getattr(request_item, 'object_type', 'unknown')
                )
            
            results.append(result)
            
            if result.success:
                successful += 1
            else:
                failed += 1
                errors.append(SyncErrorDetail(
                    error_code="SYNC_FAILED",
                    error_message=result.message
                ))
                
        except Exception as e:
            failed += 1
            error_detail = SyncErrorDetail(
                error_code="PROCESSING_ERROR",
                error_message=str(e)
            )
            errors.append(error_detail)
            
            results.append(SyncResponse(
                success=False,
                message=f"Processing error: {str(e)}",
                object_id=getattr(request_item, 'object_id', 'unknown'),
                object_type=getattr(request_item, 'object_type', 'unknown')
            ))
    
    return SyncBatchResponse(
        results=results,
        total_requested=len(batch_request.requests),
        successful=successful,
        failed=failed,
        errors=errors if errors else None
    )


@router.get("/health")
async def sync_health_check():
    """
    Simple health check for sync service.
    """
    return {
        "status": "healthy",
        "service": "sync",
        "timestamp": int(time.time() * 1000)
    }


@router.get("/stats")
async def sync_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get sync statistics for the current user.
    """
    # This is a placeholder for sync statistics
    # In a full implementation, you'd query sync status from database
    return {
        "user_id": current_user.user_id,
        "frames_synced": 0,
        "captures_synced": 0,
        "last_sync": None,
        "pending_sync_items": 0
    }