# Weld Defect Detection System - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Creating Your Account](#creating-your-account)
3. [First Login & Navigation](#first-login--navigation)
4. [Starting an Inspection Session](#starting-an-inspection-session)
5. [Camera Setup & Usage](#camera-setup--usage)
6. [Understanding AI Detection Results](#understanding-ai-detection-results)
7. [Managing Inspection Data](#managing-inspection-data)
8. [Working Offline & Data Sync](#working-offline--data-sync)
9. [Best Practices for Weld Inspection](#best-practices-for-weld-inspection)
10. [Troubleshooting Common Issues](#troubleshooting-common-issues)
11. [Browser Requirements & Optimization](#browser-requirements--optimization)

---

## Getting Started

The **Weld Defect Detection System** is a powerful, AI-powered web application designed to help inspectors detect and analyze weld defects in real-time using advanced computer vision technology. The system works entirely in your web browser and can function offline, making it perfect for industrial environments.

### Key Features
- **Real-time AI Detection**: Powered by YOLOv8 AI model for instant weld defect identification
- **Offline-First Design**: Works without internet connection, syncs when available
- **Professional Camera Integration**: Uses your device's camera with optimized settings
- **Comprehensive Reporting**: Detailed inspection records with confidence scores
- **Secure Data Management**: All inspection data is encrypted and stored safely

---

## Creating Your Account

### Registration Process
1. Navigate to the system URL provided by your administrator
2. Click **"Sign up here"** on the login page
3. Fill out the registration form with:
   - **Username**: Choose a unique identifier (3+ characters, letters/numbers/underscores only)
   - **Email Address**: Your professional email for account recovery
   - **Full Name**: Your complete name as it should appear in inspection reports
   - **Password**: Must contain at least one uppercase letter, lowercase letter, and number
   - **Role**: Select "Inspector" (most users) or "Administrator" if authorized

4. Check the box to agree to Terms and Conditions
5. Click **"Create Account"**

### Account Verification
- Your account will be activated immediately upon registration
- You'll be automatically logged in and redirected to the main dashboard
- Keep your login credentials secure and don't share them with others

---

## First Login & Navigation

### Signing In
1. Enter your **Username** and **Password**
2. Check **"Remember me"** to stay logged in on trusted devices
3. Click **"Sign In"**

### Main Dashboard Overview
After logging in, you'll see the main dashboard with:

- **Header Bar**: Shows "Weld Detection System" title and your welcome message
- **Connection Status**: Indicates online/offline status (top right)
- **User Controls**: Your username, session options, and logout button
- **New Session Form**: Central form to start a new inspection
- **Feature Cards**: Highlighting system capabilities (Real-time Detection, Offline Design, Smart Sync)

### Navigation Options
- **"New Session"** button: Create a new inspection session (default view)
- **"View Sessions"** button: See all your previous inspection sessions
- **Logout**: Safely sign out of your account

---

## Starting an Inspection Session

### Session Information Required
Before you can begin detecting weld defects, you need to create an inspection session with these details:

1. **Model Number (Type of Frame)**: 
   - The specific model or type designation of the welded structure
   - Example: "WF-2024A", "BEAM-XL-500"
   - Must be at least 2 characters

2. **Machine Serial Number**:
   - Unique identifier of the welding machine or structure being inspected
   - Example: "MSN-789456", "WM-2024-001"
   - Must be at least 3 characters

3. **Inspector Name**:
   - Your full name (automatically filled from your profile)
   - Can be edited if needed for the specific inspection
   - Must be at least 2 characters

### Creating a Session
1. Fill out the session form on the main dashboard
2. Double-check all information for accuracy
3. Click **"Start Detection Session"**
4. The system will create your session and navigate to the detection interface

**Important**: Session information appears on all inspection reports, so ensure accuracy before proceeding.

---

## Camera Setup & Usage

### Detection Interface Layout
The detection page is split into two main panels:

**Left Panel - Camera Interface**:
- Live camera feed in a square format (optimized for AI detection)
- Camera controls at the bottom
- Model loading status and performance metrics
- Resolution indicator showing camera quality

**Right Panel - History & Management**:
- Session information banner at the top
- Capture history showing all your inspection photos
- Detailed statistics and sync status
- Report generation options

### Camera Activation
1. **Grant Camera Permission**: When first accessing the detection page, your browser will ask for camera permission. Click "Allow" to proceed.

2. **Camera Auto-Start**: The system automatically attempts to start your camera with optimal settings (1280x720 resolution).

3. **Manual Camera Control**: If the camera doesn't start automatically:
   - Look for the "Start Camera" button in the camera panel
   - Click it to manually activate the camera

### AI Model Loading
Before you can take inspection photos, the AI detection model must load:

1. **Loading Screen**: You'll see a loading overlay with progress percentage
2. **Model Download**: The system downloads the YOLOv8 AI model (may take 1-2 minutes on first use)
3. **Model Warm-up**: The system prepares the model for optimal performance
4. **Ready State**: The loading overlay disappears when ready

**Important**: Do not take photos until the AI model is fully loaded. The capture button will be disabled until ready.

### Taking Inspection Photos

#### Positioning Guidelines
- **Distance**: Hold the camera 12-18 inches from the weld area
- **Lighting**: Ensure good, even lighting on the weld surface
- **Angle**: Position the camera perpendicular to the weld for best results
- **Stability**: Keep the camera steady to avoid blurry images
- **Coverage**: Frame the weld area to fill most of the camera view

#### Capture Process
1. **Position**: Align the weld area within the camera frame
2. **Focus**: Ensure the weld is clearly visible and in focus
3. **Capture**: Click the camera button (shutter icon) at the bottom of the camera panel
4. **Processing**: The system will:
   - Capture the image in square format (cropped from camera feed)
   - Run AI detection analysis
   - Generate processed image with detection overlays
   - Save both original and processed images locally

#### Capture Controls
- **Camera Button (📷)**: Take a photo with AI analysis
- **Camera Toggle**: Turn camera on/off to save battery
- **Settings Indicator**: Shows current camera resolution

---

## Understanding AI Detection Results

### Detection Process
When you take a photo, the AI system:
1. **Image Processing**: Converts your photo to the optimal format (640x640 pixels)
2. **AI Analysis**: Runs the YOLOv8 model to identify potential defects
3. **Result Generation**: Creates detection boxes and confidence scores
4. **Visual Overlay**: Adds colored rectangles around detected objects

### Reading Detection Results

#### Confidence Scores
- **Range**: 0% to 100% confidence for each detection
- **Threshold**: System shows detections above 25% confidence
- **Color Coding**: 
  - Green boxes: High confidence (75%+)
  - Yellow boxes: Medium confidence (50-75%)
  - Red boxes: Lower confidence (25-50%)

#### Detection Information
Each detected object shows:
- **Bounding Box**: Rectangle highlighting the detected area
- **Class Label**: Type of defect or object detected
- **Confidence Percentage**: How certain the AI is about the detection

### Performance Metrics
The system tracks AI performance:
- **Total Processing Time**: Complete analysis duration
- **Inference Time**: AI model calculation time
- **Preprocessing Time**: Image preparation time
- **Postprocessing Time**: Result formatting time

**Tip**: Click "Show Metrics" in the camera panel to view detailed performance data.

### Interpreting Results
- **Multiple Detections**: Multiple boxes may appear for complex weld areas
- **Overlapping Boxes**: Normal when multiple defects are close together
- **No Detections**: Either no defects present or defects below confidence threshold
- **False Positives**: AI may occasionally detect non-defects; use professional judgment

---

## Managing Inspection Data

### Viewing Capture History
The right panel shows all photos taken in the current session:

#### History List Features
- **Thumbnail Preview**: Small preview of each captured image
- **Timestamp**: When each photo was taken
- **Detection Count**: Number of objects detected in each image
- **Sync Status**: Whether the data has been uploaded to the server

#### Detailed View
Click any capture in the history to see:
- **Full-Size Images**: Both original and processed versions
- **Detection Details**: Complete list of all detected objects with confidence scores
- **Image Metadata**: Capture time, image size, processing duration
- **Export Options**: Download images or include in reports

### Session Management

#### Session Information Banner
At the top of the detection page, you'll see:
- **Current Session Details**: Model number, machine serial, inspector name
- **Frame ID**: Unique identifier for this inspection session
- **Edit Options**: Modify session information if needed
- **Session Controls**: Switch to different sessions or create new ones

#### Session Statistics
At the bottom of the history panel:
- **Total Captures**: Number of photos taken in this session
- **Objects Detected**: Total number of defects/objects found across all photos
- **Average Confidence**: Mean confidence score across all detections
- **Sync Status**: How many captures are synced, pending, or failed

### Data Export and Reporting

#### PDF Report Generation
1. Click the **"Generate PDF Report"** button in the history panel
2. The system creates a comprehensive report including:
   - Session information and inspector details
   - All captured images with detection overlays
   - Detailed analysis results and statistics
   - Timestamp and metadata for each capture

#### Individual Image Export
1. Click on any capture in the history
2. In the detailed view, use download options to save:
   - Original captured image
   - Processed image with detection overlays
   - Detection data in JSON format (for advanced users)

---

## Working Offline & Data Sync

### Offline-First Design
The system is designed to work without internet connection:

#### Local Data Storage
- **All photos and data** are stored locally on your device using advanced browser storage
- **AI detection** runs entirely on your device - no internet required
- **Session information** is saved locally and persists between browser sessions
- **Full functionality** available offline including photo capture and analysis

#### Connection Status
The connection indicator (top right of screen) shows:
- **Green**: Online and connected to server
- **Red**: Offline or connection issues
- **Yellow**: Limited connectivity

### Data Synchronization

#### Automatic Sync
When online, the system automatically:
- **Uploads** new inspection data to the server
- **Downloads** any updates from other devices/inspectors
- **Resolves** conflicts if the same data was edited in multiple places
- **Maintains** data consistency across all your devices

#### Sync Status Indicators
Each capture shows its sync status:
- **✅ Synced**: Data successfully uploaded to server
- **⏳ Pending**: Waiting to upload (will sync when online)
- **❌ Failed**: Sync error occurred (will retry automatically)

#### Manual Sync Control
- **Refresh Button**: Manually trigger sync in the history panel
- **Retry Failed**: Automatically retries failed uploads
- **Sync Statistics**: View detailed sync progress and status

#### Sync Best Practices
1. **Work Offline**: Don't worry about internet connectivity during inspections
2. **Sync Regularly**: Connect to internet periodically to backup data
3. **Check Status**: Monitor sync indicators to ensure data is preserved
4. **Battery Management**: Sync operations are optimized to preserve battery life

---

## Best Practices for Weld Inspection

### Pre-Inspection Setup
1. **Clean Work Area**: Ensure the weld surface is clean and well-lit
2. **Camera Check**: Verify camera is working and focused properly
3. **Session Setup**: Double-check session information before starting
4. **Battery Level**: Ensure device has sufficient battery for the inspection

### During Inspection
1. **Systematic Coverage**: Photograph all weld segments methodically
2. **Multiple Angles**: Take photos from different perspectives for complex welds
3. **Consistent Lighting**: Maintain similar lighting conditions throughout
4. **Steady Hands**: Keep camera stable to ensure clear, analyzable images
5. **Safety First**: Follow all workplace safety protocols while using the device

### Photo Quality Guidelines

#### Optimal Conditions
- **Lighting**: Bright, even illumination without harsh shadows
- **Distance**: 12-18 inches from weld surface
- **Focus**: Sharp, clear image of the weld area
- **Framing**: Weld fills majority of the frame
- **Stability**: Minimal motion blur

#### What to Avoid
- **Poor Lighting**: Dark areas, harsh shadows, or glare
- **Wrong Distance**: Too far (details lost) or too close (out of focus)
- **Blur**: Camera shake or rapid movement
- **Obstructions**: Tools, hands, or debris blocking the weld view
- **Extreme Angles**: Highly angled shots that distort the weld geometry

### Data Management Best Practices
1. **Regular Sync**: Connect to internet every few hours to backup data
2. **Session Organization**: Use clear, descriptive session information
3. **Review Results**: Check AI detections and add notes as needed
4. **Report Generation**: Create PDF reports promptly after inspections
5. **Data Backup**: Ensure important inspection data is synced before closing

### Quality Assurance
1. **AI Assistance**: Use AI detections as guidance, not final judgment
2. **Professional Review**: Apply your expertise to interpret results
3. **Documentation**: Maintain detailed records of inspection conditions
4. **Follow-up**: Re-inspect areas with low confidence scores if needed
5. **Calibration**: Regularly verify AI accuracy with known defect samples

---

## Troubleshooting Common Issues

### Camera Problems

#### Camera Won't Start
**Symptoms**: Black screen, "Camera inactive" message
**Solutions**:
1. Click "Start Camera" button in the camera panel
2. Refresh the page and grant camera permissions when prompted
3. Check if another application is using the camera
4. Try different browsers (Chrome, Firefox, Safari)
5. Restart your device if camera remains unavailable

#### Poor Image Quality
**Symptoms**: Blurry, dark, or pixelated images
**Solutions**:
1. Clean camera lens with soft cloth
2. Improve lighting conditions
3. Move closer to optimal distance (12-18 inches)
4. Ensure camera is not damaged
5. Try different device if quality doesn't improve

#### Camera Permissions Denied
**Symptoms**: "Camera access denied" error
**Solutions**:
1. Check browser address bar for camera icon
2. Click camera icon and select "Allow"
3. Go to browser settings and enable camera for the site
4. Clear browser cache and refresh page
5. Try private/incognito browsing mode

### AI Detection Issues

#### Model Won't Load
**Symptoms**: Stuck at "Loading AI Model" screen
**Solutions**:
1. Wait 2-3 minutes for initial download (first use only)
2. Check internet connection for model download
3. Clear browser cache and reload page
4. Try different browser or device
5. Contact administrator if problem persists

#### No Detections Found
**Symptoms**: AI processes but finds no defects
**Possible Causes**:
- Weld quality is actually good (no defects present)
- Lighting conditions prevent detection
- Camera too far from weld surface
- Defects below confidence threshold (25%)

**Solutions**:
1. Adjust lighting and camera position
2. Move closer to weld surface
3. Try different angles or perspectives
4. Use professional judgment to evaluate results

#### Inconsistent Results
**Symptoms**: Different results for similar images
**Solutions**:
1. Maintain consistent lighting and positioning
2. Ensure camera stability (avoid motion blur)
3. Take multiple photos for verification
4. Apply professional expertise to interpret variations

### Data and Sync Issues

#### Data Not Syncing
**Symptoms**: Captures showing "Pending" or "Failed" status
**Solutions**:
1. Check internet connection status (top right indicator)
2. Click manual refresh button in history panel
3. Wait for automatic retry (occurs every few minutes)
4. Ensure you're logged in properly
5. Contact administrator if sync consistently fails

#### Missing Captures
**Symptoms**: Previously taken photos not visible
**Solutions**:
1. Check if you're in the correct session
2. Click refresh button to reload history
3. Verify session information matches previous work
4. Check if captures are in a different session (View Sessions)
5. Data may still be syncing - wait and refresh

#### Session Information Errors
**Symptoms**: Wrong model number, machine serial, or inspector name
**Solutions**:
1. Click "Edit Session" in the session banner
2. Update incorrect information
3. Changes apply to new captures only
4. Create new session if major corrections needed

### Performance Issues

#### Slow Processing
**Symptoms**: Long delays between capture and results
**Solutions**:
1. Close other browser tabs and applications
2. Ensure device has sufficient available memory
3. Restart browser if performance degrades
4. Use newer device with better processing power
5. Clear browser cache to free up storage

#### Browser Crashes
**Symptoms**: Browser becomes unresponsive or closes
**Solutions**:
1. Restart browser and reload the application
2. Close unnecessary browser tabs
3. Update browser to latest version
4. Clear browser cache and cookies
5. Use different browser if crashes persist

#### Battery Drain
**Symptoms**: Device battery depletes quickly
**Solutions**:
1. Reduce screen brightness when possible
2. Close camera when not actively inspecting
3. Enable battery saver mode on device
4. Sync data regularly to reduce background processing
5. Use device charger during extended inspections

---

## Browser Requirements & Optimization

### Supported Browsers
The system works best with modern browsers:

#### Recommended Browsers
1. **Google Chrome** (version 90+) - Best performance and compatibility
2. **Mozilla Firefox** (version 85+) - Good performance, privacy-focused
3. **Microsoft Edge** (Chromium-based, version 90+) - Windows integration
4. **Safari** (version 14+) - macOS and iOS devices

#### Mobile Browsers
- **Chrome Mobile** (Android/iOS)
- **Safari Mobile** (iOS)
- **Firefox Mobile** (Android)
- **Samsung Internet** (Samsung devices)

### Device Requirements

#### Minimum Specifications
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for browser cache
- **Camera**: Any built-in or external camera with 720p resolution
- **Internet**: Not required for detection, needed for sync only

#### Optimal Specifications
- **RAM**: 8GB or more for smooth performance
- **Storage**: 5GB+ free space for extensive data storage
- **Camera**: 1080p or higher for best image quality
- **Processor**: Modern CPU (last 5 years) for fast AI processing

### Browser Optimization Settings

#### Chrome Optimization
1. **Enable Hardware Acceleration**:
   - Settings → Advanced → System
   - Turn on "Use hardware acceleration when available"

2. **Camera Settings**:
   - Settings → Privacy and Security → Site Settings → Camera
   - Allow camera access for the inspection site

3. **Storage Management**:
   - Settings → Privacy and Security → Site Settings → Storage
   - Allow sufficient storage for the application

#### Performance Tips
1. **Close Unused Tabs**: Keep only necessary browser tabs open
2. **Update Browser**: Use the latest browser version for best performance
3. **Clear Cache**: Regularly clear browser cache (weekly recommended)
4. **Disable Extensions**: Turn off unnecessary browser extensions
5. **Restart Regularly**: Restart browser daily for optimal performance

### Network Configuration

#### For Online Use
- **Bandwidth**: 1 Mbps minimum for sync operations
- **Latency**: Low latency preferred for responsive sync
- **Firewall**: Ensure application domain is not blocked

#### For Offline Use
- **No Requirements**: System works completely offline
- **Local Storage**: Relies on browser's IndexedDB storage
- **Sync Later**: Data uploads when connection restored

### Troubleshooting Browser Issues

#### Clear Browser Data
1. Chrome: Settings → Privacy and Security → Clear browsing data
2. Firefox: Settings → Privacy & Security → Clear Data
3. Safari: Develop → Empty Caches (or Settings → Privacy)
4. Edge: Settings → Privacy & Security → Clear browsing data

#### Reset Browser Settings
If problems persist:
1. Create backup of important bookmarks
2. Reset browser to default settings
3. Reinstall browser if necessary
4. Test application with fresh browser installation

#### Alternative Solutions
- **Try Different Browser**: Test with multiple browsers
- **Private/Incognito Mode**: Bypass extension conflicts
- **Different Device**: Verify if issue is device-specific
- **Administrator Contact**: Report persistent technical issues

---

## Getting Help

### In-Application Support
- **Performance Metrics**: Use "Show Metrics" for technical diagnostics
- **Connection Status**: Monitor online/offline indicator
- **Error Messages**: Note exact error text for troubleshooting
- **Browser Console**: Press F12 for advanced debugging (technical users)

### Contact Information
- **System Administrator**: Contact your organization's IT department
- **Technical Support**: Report bugs or technical issues through proper channels
- **Training**: Request additional training sessions for your team

### Best Practices for Getting Help
1. **Document the Issue**: Note exact steps that caused the problem
2. **Include Details**: Browser type, device model, error messages
3. **Screenshots**: Capture images of error screens when possible
4. **Test Different Conditions**: Try different browsers, devices, or networks
5. **Check This Guide**: Review relevant troubleshooting sections first

---

## Conclusion

The Weld Defect Detection System provides professional-grade AI-powered inspection capabilities directly in your web browser. By following this guide and applying best practices, you'll be able to conduct thorough, documented weld inspections with confidence.

Remember:
- **AI is a Tool**: Use AI detections to enhance, not replace, your professional expertise
- **Data Security**: Your inspection data is protected with enterprise-grade security
- **Offline Capability**: Work confidently without internet connectivity
- **Continuous Improvement**: The system learns and improves over time

For the most current information and updates, always refer to the latest version of this documentation and consult with your system administrator.

---