// src/lib/db/syncQueueOperations.ts
// Fixed sync queue operations with proper idb library usage

import { getDB } from './index';
import type { SyncQueueItem } from './types';

/**
 * Get pending sync items ordered by priority
 */
export async function getPendingSyncItems(limit: number = 50): Promise<SyncQueueItem[]> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readonly');
  const index = tx.objectStore('syncQueue').index('by-status-priority');
  
  // Get pending items only
  const items = await index.getAll(IDBKeyRange.bound(['pending', 0], ['pending', Number.MAX_SAFE_INTEGER]));
  
  // Sort by priority (descending) then by creation time (ascending)
  const sortedItems = items.sort((a, b) => {
    if (a.priority !== b.priority) {
      return b.priority - a.priority; // Higher priority first
    }
    return a.createdAt - b.createdAt; // Older items first
  });
  
  await tx.done;
  return sortedItems.slice(0, limit);
}

/**
 * Update a sync queue item
 */
export async function updateSyncQueueItem(item: SyncQueueItem): Promise<void> {
  const db = await getDB();
  await db.put('syncQueue', item);
}

/**
 * Delete a sync queue item by queueId
 */
export async function deleteSyncQueueItem(queueId: number): Promise<void> {
  const db = await getDB();
  await db.delete('syncQueue', queueId);
}

/**
 * Get sync statistics
 */
export async function getSyncStats(): Promise<{
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}> {
  const db = await getDB();
  const allItems = await db.getAll('syncQueue');
  
  const stats = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  };
  
  allItems.forEach(item => {
    if (item.status in stats) {
      stats[item.status as keyof typeof stats]++;
    }
  });
  
  return stats;
}

/**
 * Add item to sync queue
 */
export async function addToSyncQueue(item: Omit<SyncQueueItem, 'queueId'>): Promise<number> {
  const db = await getDB();
  return await db.add('syncQueue', item);
}

/**
 * Clear completed sync items from queue
 */
export async function clearCompletedSyncItems(): Promise<void> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readwrite');
  const index = tx.objectStore('syncQueue').index('by-status-priority');
  
  // Get all completed items
  const completedItems = await index.getAll(IDBKeyRange.bound(['completed', 0], ['completed', Number.MAX_SAFE_INTEGER]));
  
  // Delete each completed item
  await Promise.all(
    completedItems.map(item => {
      if (item.queueId !== undefined) {
        return tx.objectStore('syncQueue').delete(item.queueId);
      }
      return Promise.resolve();
    })
  );
  
  await tx.done;
}

/**
 * Get all failed sync items
 */
export async function getFailedSyncItems(): Promise<SyncQueueItem[]> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readonly');
  const index = tx.objectStore('syncQueue').index('by-status-priority');
  
  // Get failed items only
  const failedItems = await index.getAll(IDBKeyRange.bound(['failed', 0], ['failed', Number.MAX_SAFE_INTEGER]));
  
  await tx.done;
  return failedItems;
}

/**
 * Reset failed items to pending status
 */
export async function retryFailedItems(): Promise<void> {
  const failedItems = await getFailedSyncItems();
  
  for (const item of failedItems) {
    await updateSyncQueueItem({
      ...item,
      status: 'pending',
      attemptCount: 0,
      lastAttempt: Date.now()
    });
  }
}

/**
 * Get all sync items with specific status
 */
export async function getSyncItemsByStatus(status: 'pending' | 'processing' | 'completed' | 'failed'): Promise<SyncQueueItem[]> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readonly');
  const index = tx.objectStore('syncQueue').index('by-status-priority');
  
  const items = await index.getAll(IDBKeyRange.bound([status, 0], [status, Number.MAX_SAFE_INTEGER]));
  
  await tx.done;
  return items;
}

/**
 * Get sync queue item by ID
 */
export async function getSyncQueueItem(queueId: number): Promise<SyncQueueItem | undefined> {
  const db = await getDB();
  return await db.get('syncQueue', queueId);
}

/**
 * Get pending sync items for specific frame
 */
export async function getPendingSyncItemsForFrame(frameId: string, limit: number = 50): Promise<SyncQueueItem[]> {
  const db = await getDB();
  const tx = db.transaction(['syncQueue', 'captures'], 'readonly');
  
  // Get all pending items
  const index = tx.objectStore('syncQueue').index('by-status-priority');
  const allPendingItems = await index.getAll(IDBKeyRange.bound(['pending', 0], ['pending', Number.MAX_SAFE_INTEGER]));
  
  // Filter items that belong to this frame
  const frameItems: SyncQueueItem[] = [];
  
  for (const item of allPendingItems) {
    let belongsToFrame = false;
    
    // Direct frame match
    if (item.objectType === 'frame' && item.objectId === frameId) {
      belongsToFrame = true;
    }
    // Frame ID stored in context
    else if (item.context?.frameId === frameId) {
      belongsToFrame = true;
    }
    // For captures without context, check the capture's frameId
    else if (item.objectType === 'capture' && !item.context?.frameId) {
      try {
        const capture = await tx.objectStore('captures').get(item.objectId);
        if (capture && capture.frameId === frameId) {
          belongsToFrame = true;
          // Update the item with frame context for future queries
          item.context = { frameId };
        }
      } catch (error) {
        console.warn('Error checking capture frameId:', error);
      }
    }
    
    if (belongsToFrame) {
      frameItems.push(item);
    }
  }
  
  // Sort by priority (descending) then by creation time (ascending)
  const sortedItems = frameItems.sort((a, b) => {
    if (a.priority !== b.priority) {
      return b.priority - a.priority; // Higher priority first
    }
    return a.createdAt - b.createdAt; // Older items first
  });
  
  await tx.done;
  return sortedItems.slice(0, limit);
}

/**
 * Get sync statistics for specific frame
 */
export async function getFrameSyncStats(frameId: string): Promise<{
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}> {
  const db = await getDB();
  const tx = db.transaction(['syncQueue', 'captures'], 'readonly');
  
  const allItems = await tx.objectStore('syncQueue').getAll();
  
  const stats = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  };
  
  for (const item of allItems) {
    let belongsToFrame = false;
    
    // Check if item belongs to this frame
    if (item.objectType === 'frame' && item.objectId === frameId) {
      belongsToFrame = true;
    } else if (item.context?.frameId === frameId) {
      belongsToFrame = true;
    } else if (item.objectType === 'capture' && !item.context?.frameId) {
      try {
        const capture = await tx.objectStore('captures').get(item.objectId);
        if (capture && capture.frameId === frameId) {
          belongsToFrame = true;
        }
      } catch (error) {
        console.warn('Error checking capture frameId:', error);
      }
    }
    
    if (belongsToFrame && item.status in stats) {
      stats[item.status as keyof typeof stats]++;
    }
  }
  
  await tx.done;
  return stats;
}