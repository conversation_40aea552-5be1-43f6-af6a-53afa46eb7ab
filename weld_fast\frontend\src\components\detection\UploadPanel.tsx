'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Alert<PERSON>ircle, Wifi, WifiOff, Loader2, Upload } from 'lucide-react';
import { useSession } from '@/context/SessionContext';
import { useOptionalCaptureUpdates } from '@/hooks/useOptionalCaptureUpdates';
import { broadcastRefreshTrigger } from '@/hooks/useOrchestratedRefresh';
import { modelService } from '@/lib/detection';
import { BatchProcessor, createBatchProcessor, BatchProgress, ProcessingResult } from '@/lib/upload/batchProcessor';
import FileDropZone, { UploadFile } from './FileDropZone';
import UploadResults from './UploadResults';
import * as tf from '@tensorflow/tfjs';

interface UploadPanelProps {
  onViewCapture?: (captureId: string) => void;
}

export default function UploadPanel({ onViewCapture }: UploadPanelProps) {
  const { frameId } = useSession();
  const captureUpdates = useOptionalCaptureUpdates();

  // State management
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState<BatchProgress | null>(null);
  const [results, setResults] = useState<ProcessingResult[]>([]);
  const [error, setError] = useState<string | null>(null);

  // AI Model state
  const [model, setModel] = useState<tf.GraphModel | null>(null);
  const [modelLoading, setModelLoading] = useState(false);
  const [modelError, setModelError] = useState<string | null>(null);

  // Processing references
  const batchProcessorRef = useRef<BatchProcessor | null>(null);

  // Load AI model
  useEffect(() => {
    let mounted = true;

    async function loadDetectionModel() {
      try {
        setModelLoading(true);
        setModelError(null);

        // Initialize TensorFlow.js
        await tf.ready();

        // Get optimal model configuration
        const config = modelService.getOptimalModelConfig();

        // Load model
        const loadedModel = await modelService.loadModel(config.modelName);

        if (mounted) {
          // Warm up model
          await modelService.warmupModel(loadedModel, config.inputSize);
          setModel(loadedModel);
        }
      } catch (error) {
        console.error('Error loading model:', error);
        if (mounted) {
          setModelError(error instanceof Error ? error.message : 'Failed to load AI model');
        }
      } finally {
        if (mounted) {
          setModelLoading(false);
        }
      }
    }

    loadDetectionModel();

    return () => {
      mounted = false;
    };
  }, []);

  // Initialize batch processor
  useEffect(() => {
    if (model && frameId) {
      batchProcessorRef.current = createBatchProcessor(model, frameId, {
        concurrentLimit: 3,
        confidenceThreshold: 0.25,
        iouThreshold: 0.45,
        maxDetections: 100
      });
    }
  }, [model, frameId]);

  // Update batch processor when model or frameId changes
  useEffect(() => {
    if (batchProcessorRef.current) {
      batchProcessorRef.current.updateModel(model);
      if (frameId) {
        batchProcessorRef.current.updateFrameId(frameId);
      }
    }
  }, [model, frameId]);

  // Handle file selection
  const handleFilesSelected = useCallback((newFiles: UploadFile[]) => {
    setFiles(prev => [...prev, ...newFiles]);
    setError(null);
  }, []);

  // Handle file removal
  const handleRemoveFile = useCallback((fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
    
    // Remove from results if exists
    setResults(prev => prev.filter(r => r.fileId !== fileId));
  }, []);

  // Handle file status updates during processing
  const handleFileStatus = useCallback((fileId: string, status: 'processing' | 'completed' | 'error', result?: ProcessingResult) => {
    // Update file status
    setFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { 
            ...file, 
            status: status === 'completed' ? 'completed' : status === 'error' ? 'error' : 'processing',
            error: result?.error 
          }
        : file
    ));

    // Update results
    if (result) {
      setResults(prev => {
        const existingIndex = prev.findIndex(r => r.fileId === fileId);
        if (existingIndex >= 0) {
          const newResults = [...prev];
          newResults[existingIndex] = result;
          return newResults;
        } else {
          return [...prev, result];
        }
      });

      // Trigger UI updates for successful captures
      if (result.success && result.captureId) {
        setTimeout(() => {
          broadcastRefreshTrigger('weld-capture-updates', {
            type: 'capture-created',
            captureId: result.captureId!,
            frameId: frameId!,
            timestamp: Date.now()
          });

          if (captureUpdates) {
            captureUpdates.triggerRefresh();
            captureUpdates.broadcastChange('capture-created', { 
              captureId: result.captureId!, 
              frameId: frameId! 
            });
          }
        }, 100);
      }
    }
  }, [frameId, captureUpdates]);

  // Start processing
  const handleStartProcessing = useCallback(async () => {
    if (!batchProcessorRef.current || !frameId || !model || files.length === 0) {
      setError('Cannot start processing: Missing required data');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);
      setProgress({
        total: files.length,
        completed: 0,
        failed: 0,
        processing: 0,
        percentage: 0
      });

      // Reset file statuses
      setFiles(prev => prev.map(file => ({ ...file, status: 'pending', error: undefined })));
      setResults([]);

      // Start batch processing
      await batchProcessorRef.current.processBatch(
        files,
        setProgress,
        handleFileStatus
      );

    } catch (error) {
      console.error('Batch processing failed:', error);
      setError(error instanceof Error ? error.message : 'Processing failed');
    } finally {
      setIsProcessing(false);
    }
  }, [files, frameId, model, handleFileStatus]);

  // Stop processing
  const handleStopProcessing = useCallback(() => {
    if (batchProcessorRef.current) {
      batchProcessorRef.current.abortProcessing();
    }
    setIsProcessing(false);
  }, []);

  // Clear all files
  const handleClearAll = useCallback(() => {
    files.forEach(file => URL.revokeObjectURL(file.preview));
    setFiles([]);
    setResults([]);
    setProgress(null);
    setError(null);
  }, [files]);

  // Check if system is ready
  const isReady = !modelLoading && !modelError && model && frameId;
  const hasFiles = files.length > 0;

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Simplified Header */}
      <div className="flex-shrink-0 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Upload className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Batch Image Processing
              </h2>
              <div className="flex items-center space-x-4 text-sm">
                {/* AI Status */}
                {modelLoading && (
                  <div className="flex items-center space-x-1 text-blue-600 dark:text-blue-400">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Loading AI...</span>
                  </div>
                )}
                {modelError && (
                  <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
                    <WifiOff className="h-3 w-3" />
                    <span>AI Error</span>
                  </div>
                )}
                {isReady && (
                  <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                    <Wifi className="h-3 w-3" />
                    <span>AI Ready</span>
                  </div>
                )}
                {hasFiles && (
                  <span className="text-gray-500 dark:text-gray-400">
                    {files.length} {files.length === 1 ? 'file' : 'files'} selected
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {hasFiles && (
              <button
                onClick={handleClearAll}
                disabled={isProcessing}
                className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Clear All
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto px-6 pb-6">
        {/* Error Alert */}
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Model States */}
        {modelError && !modelLoading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center max-w-md">
              <div className="p-4 bg-red-100 dark:bg-red-900/20 rounded-full w-20 h-20 mx-auto mb-4">
                <WifiOff className="w-12 h-12 text-red-500 mx-auto mt-2" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                AI Model Error
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {modelError}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Reload Page
              </button>
            </div>
          </div>
        ) : modelLoading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-full w-20 h-20 mx-auto mb-4">
                <Loader2 className="w-12 h-12 text-blue-600 mx-auto mt-2 animate-spin" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Preparing AI Engine
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Loading the detection model...
              </p>
            </div>
          </div>
        ) : !frameId ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center max-w-md">
              <div className="p-4 bg-orange-100 dark:bg-orange-900/20 rounded-full w-20 h-20 mx-auto mb-4">
                <AlertCircle className="w-12 h-12 text-orange-500 mx-auto mt-2" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Active Session
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Please create or select a detection session to upload images.
              </p>
            </div>
          </div>
        ) : (
          /* Main Upload Interface */
          <div className="space-y-6">
            {/* File Drop Zone */}
            <FileDropZone
              onFilesSelected={handleFilesSelected}
              onRemoveFile={handleRemoveFile}
              selectedFiles={files}
              disabled={isProcessing || !isReady}
            />

            {/* Processing Section */}
            {hasFiles && (
              <div className="space-y-6">
                {/* Processing Controls */}
                <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Batch Processing
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {isProcessing ? 'Processing your images...' : `Ready to process ${files.length} ${files.length === 1 ? 'image' : 'images'}`}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {!isProcessing ? (
                        <button
                          onClick={handleStartProcessing}
                          disabled={!isReady || files.length === 0}
                          className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                        >
                          <Wifi className="h-5 w-5" />
                          <span>Start Processing</span>
                        </button>
                      ) : (
                        <button
                          onClick={handleStopProcessing}
                          className="flex items-center space-x-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
                        >
                          <AlertCircle className="h-5 w-5" />
                          <span>Stop Processing</span>
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {progress && (
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300">
                        <span>Progress</span>
                        <span>{progress.percentage}% ({progress.completed + progress.failed}/{progress.total})</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress.percentage}%` }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span className="text-green-600 dark:text-green-400">{progress.completed} completed</span>
                        <span className="text-red-600 dark:text-red-400">{progress.failed} failed</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Results Grid */}
                {results.length > 0 && (
                  <UploadResults
                    results={results}
                    files={files}
                    onViewCapture={onViewCapture}
                  />
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}