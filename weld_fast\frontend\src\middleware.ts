import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected routes and their access requirements
const protectedRoutes = {
  '/detection': { requireAuth: true, roles: ['admin', 'inspector'] },
  '/profile': { requireAuth: true, roles: ['admin', 'inspector'] },
  '/admin': { requireAuth: true, roles: ['admin'] },
} as const;

// const publicRoutes = ['/login', '/register', '/forgot-password', '/reset-password'];
const authRoutes = ['/login', '/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const tokens = request.cookies.get('auth_tokens')?.value;
  const user = request.cookies.get('auth_user')?.value;

  let isAuthenticated = false;
  let userRole: string | null = null;

  // Check if user is authenticated
  if (tokens && user) {
    try {
      const decodedUser = decodeURIComponent(user);
      const userData = JSON.parse(decodedUser);
      const decodedTokens = decodeURIComponent(tokens);
      const tokensData = JSON.parse(decodedTokens);
      
      // Basic token expiry check
      if (tokensData.access_token) {
        isAuthenticated = true;
        userRole = userData.role;
      }
    } catch {
      // Invalid user data, clear cookies
      const response = NextResponse.redirect(new URL('/login', request.url));
      response.cookies.delete('auth_tokens');
      response.cookies.delete('auth_user');
      return response;
    }
  }

  // Check if route requires authentication
  const routeConfig = Object.entries(protectedRoutes).find(([route]) => 
    pathname.startsWith(route)
  );

  if (routeConfig) {
    const [, config] = routeConfig;
    
    // Route requires authentication
    if (config.requireAuth && !isAuthenticated) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Check role-based access
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (config.requireAuth && isAuthenticated && userRole && !config.roles.includes(userRole as any)) {
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }

  // Redirect authenticated users away from auth pages
  if (isAuthenticated && authRoutes.includes(pathname)) {
    return NextResponse.redirect(new URL('/detection', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - home page (/) - let it handle its own auth
     */
    '/((?!api|_next/static|_next/image|favicon.ico|$).*)',
  ],
};