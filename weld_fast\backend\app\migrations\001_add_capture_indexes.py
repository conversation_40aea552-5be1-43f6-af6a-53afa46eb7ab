"""
Migration script to add database indexes for capture operations
"""

from sqlalchemy import text
from sqlalchemy.orm import Session
from ..database import engine


def upgrade():
    """Add indexes for better capture query performance"""
    
    with Session(engine) as session:
        try:
            # Index for frame capture listing (most common query)
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_frame_id_timestamp 
                ON captures (frame_id, capture_timestamp DESC);
            """))
            
            # Index for sync operations
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_sync_status 
                ON captures (sync_status) 
                WHERE sync_status != 'synced';
            """))
            
            # Index for conflict resolution using sync_version
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_sync_version 
                ON captures (capture_id, sync_version);
            """))
            
            # Index for frame lookups in captures
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_frame_id 
                ON captures (frame_id);
            """))
            
            # Index for timestamp-based queries
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_timestamp 
                ON captures (capture_timestamp DESC);
            """))
            
            # Composite index for frame + sync status (useful for sync operations)
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_captures_frame_sync 
                ON captures (frame_id, sync_status);
            """))
            
            # Add constraints for data integrity
            
            # Ensure sync_version is always positive
            session.execute(text("""
                ALTER TABLE captures 
                ADD CONSTRAINT IF NOT EXISTS chk_sync_version_positive 
                CHECK (sync_version > 0);
            """))
            
            # Ensure capture_timestamp is reasonable (not in far future)
            session.execute(text("""
                ALTER TABLE captures 
                ADD CONSTRAINT IF NOT EXISTS chk_capture_timestamp_reasonable 
                CHECK (capture_timestamp > 1640995200 AND capture_timestamp < EXTRACT(EPOCH FROM NOW()) + 86400);
            """))
            
            # Ensure at least one detection result or note why empty
            session.execute(text("""
                ALTER TABLE captures 
                ADD CONSTRAINT IF NOT EXISTS chk_detection_results_valid 
                CHECK (
                    detection_results IS NOT NULL AND 
                    (
                        jsonb_array_length(detection_results) > 0 OR 
                        jsonb_array_length(detection_results) = 0
                    )
                );
            """))
            
            session.commit()
            print("✅ Capture indexes and constraints created successfully")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error creating capture indexes: {e}")
            raise


def downgrade():
    """Remove indexes and constraints"""
    
    with Session(engine) as session:
        try:
            # Drop indexes
            indexes_to_drop = [
                "idx_captures_frame_id_timestamp",
                "idx_captures_sync_status", 
                "idx_captures_sync_version",
                "idx_captures_frame_id",
                "idx_captures_timestamp",
                "idx_captures_frame_sync"
            ]
            
            for index_name in indexes_to_drop:
                session.execute(text(f"DROP INDEX IF EXISTS {index_name};"))
            
            # Drop constraints
            constraints_to_drop = [
                "chk_sync_version_positive",
                "chk_capture_timestamp_reasonable", 
                "chk_detection_results_valid"
            ]
            
            for constraint_name in constraints_to_drop:
                session.execute(text(f"ALTER TABLE captures DROP CONSTRAINT IF EXISTS {constraint_name};"))
            
            session.commit()
            print("✅ Capture indexes and constraints removed successfully")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error removing capture indexes: {e}")
            raise


if __name__ == "__main__":
    print("Running capture indexes migration...")
    upgrade()