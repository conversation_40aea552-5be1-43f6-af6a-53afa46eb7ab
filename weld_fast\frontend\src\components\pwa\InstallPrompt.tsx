'use client';

import { useState, useEffect } from 'react';
import { pwaService } from '@/lib/pwa/pwa-service';

export function InstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    // Initialize PWA service
    pwaService.initialize();

    // Listen for install availability
    const handleInstallAvailable = () => {
      if (!pwaService.isStandalone()) {
        setShowPrompt(true);
      }
    };

    window.addEventListener('pwa-install-available', handleInstallAvailable);
    
    return () => {
      window.removeEventListener('pwa-install-available', handleInstallAvailable);
    };
  }, []);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const accepted = await pwaService.showInstallPrompt();
      if (accepted) {
        setShowPrompt(false);
      }
    } catch (error) {
      console.error('Install failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    // Remember user's choice for this session (client-side only)
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('pwa-install-dismissed', 'true');
    }
  };

  // Don't show if already dismissed this session (client-side only)
  if (typeof window !== 'undefined' && sessionStorage.getItem('pwa-install-dismissed') === 'true') {
    return null;
  }

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 md:left-auto md:right-4 md:max-w-sm">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-sm">Install WeldDetect</h3>
          <p className="text-xs opacity-90 mt-1">
            Install our app for a better experience with offline support
          </p>
        </div>
        <button
          onClick={handleDismiss}
          className="ml-2 text-white/70 hover:text-white text-lg leading-none"
          aria-label="Dismiss"
        >
          ×
        </button>
      </div>
      
      <div className="flex gap-2 mt-3">
        <button
          onClick={handleInstall}
          disabled={isInstalling}
          className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-blue-50 disabled:opacity-50"
        >
          {isInstalling ? 'Installing...' : 'Install'}
        </button>
        <button
          onClick={handleDismiss}
          className="text-white/70 hover:text-white px-3 py-1 text-sm"
        >
          Not now
        </button>
      </div>
    </div>
  );
}