# PWA Testing Script

## Production Build Test
```bash
npm run build
npm run start
```

## Chrome DevTools Checklist

### 1. Application Tab
- **Manifest**: Should show app name, icons, theme
- **Service Workers**: Should show registered SW
- **Storage**: IndexedDB tables should be visible
- **Cache Storage**: Should show cached resources

### 2. Lighthouse PWA Audit
- Open DevTools → Lighthouse tab
- Select "Progressive Web App" 
- Run audit (should score 90+)

### 3. Network Tab
- Load app normally
- Go offline (disable network)
- Reload page - should still work
- Check cached resources loading

## Mobile Testing

### Android Chrome
1. Visit site → "Add to Home Screen" prompt
2. Install app
3. Open from home screen (standalone mode)
4. Test offline functionality

### iOS Safari
1. Share button → "Add to Home Screen"
2. Opens in standalone mode
3. Limited PWA features (iOS restrictions)

## Manual Tests

### Install Prompt
- Should appear after 2-3 visits
- Click "Install" → app installs
- Dismissible with "Not now"

### Offline Functionality
- Go offline
- App should show "Working offline" indicator
- IndexedDB data still accessible
- Sync queue builds up
- When online: data syncs automatically

### Background Sync (Production Only)
- Add detection data while online
- Go offline, add more data
- Come back online
- Data should sync automatically

### Service Worker Updates
- Deploy new version
- SW should update automatically
- App reloads with new content

## Expected Lighthouse Scores
- **PWA**: 90+ (installable, works offline)
- **Performance**: 80+ (with TensorFlow.js)
- **Accessibility**: 90+
- **Best Practices**: 95+
- **SEO**: 90+

## Common Issues
- **Install prompt not showing**: Clear browser data, visit multiple times
- **Offline not working**: Check service worker registration
- **Sync failing**: Check network/backend connectivity
- **Icons not loading**: Verify icon paths in manifest.json