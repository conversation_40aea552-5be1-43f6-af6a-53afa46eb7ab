'use client';

import { getPendingSyncItems, updateSyncQueueItem, deleteSyncQueueItem, getSyncStats, getPendingSyncItemsForFrame, getFailedSyncItems } from '@/lib/db/syncQueueOperations';
import type { SyncQueueItem } from '@/lib/db/types';
import { enhancedSyncStateManager } from './enhancedSyncStateManager';
import { reactiveDatabase } from '@/lib/db/reactiveDatabase';
import { authFetch } from '@/lib/auth/authenticatedFetch';
import type { SyncResult, SyncProgress, SyncErrorType } from './types';

// type SyncProgressCallback = (progress: SyncProgress) => void;

/**
 * Reactive Sync Service
 * 
 * Fully reactive sync service that integrates directly with enhancedSyncStateManager.
 * Replaces the legacy syncManager with a cleaner, reactive architecture.
 */
class ReactiveSyncService {
  private isProcessing = false;
  private abortController: AbortController | null = null;
  private currentSyncPromise: Promise<SyncProgress> | null = null;
  private readonly API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second
  private readonly DEFAULT_BATCH_SIZE = 5; // Process 5 items concurrently by default
  private readonly MAX_BATCH_SIZE = 10; // Maximum concurrent operations

  /**
   * Create initial sync progress object
   */
  private createInitialProgress(): SyncProgress {
    return {
      totalItems: 0,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      isProcessing: true,
      performanceMetrics: {
        itemsPerSecond: 0,
        averageBatchTime: 0,
        estimatedTimeRemaining: 0,
        startTime: Date.now()
      },
      errorSummary: {
        networkErrors: 0,
        authErrors: 0,
        serverErrors: 0,
        clientErrors: 0,
        unknownErrors: 0
      }
    };
  }

  /**
   * Update enhanced sync state manager with real-time progress
   */
  private async updateSyncState(progress?: SyncProgress): Promise<void> {
    try {
      const stats = await getSyncStats();
      
      // Update enhanced sync state manager with both stats and progress
      enhancedSyncStateManager.updateSyncState(stats, progress);
    } catch (error) {
      console.error('[ReactiveSyncService] Failed to update sync state:', error);
    }
  }

  /**
   * Process sync queue for specific frame only
   */
  async processSyncQueueForFrame(frameId: string): Promise<SyncProgress> {
    if (this.isProcessing && this.currentSyncPromise) {
      console.log('[ReactiveSyncService] Frame sync already in progress, sharing current operation for frame:', frameId);
      return await this.currentSyncPromise;
    }

    console.log('[ReactiveSyncService] Starting frame sync for:', frameId);

    this.isProcessing = true;
    this.abortController = new AbortController();

    // Create and store the promise so concurrent calls can share it
    this.currentSyncPromise = this.executeFrameSync(frameId);
    
    try {
      const result = await this.currentSyncPromise;
      return result;
    } finally {
      this.isProcessing = false;
      this.abortController = null;
      this.currentSyncPromise = null;
    }
  }

  /**
   * Execute frame sync operation (internal method)
   */
  private async executeFrameSync(frameId: string): Promise<SyncProgress> {
    const progress = this.createInitialProgress();
    progress.currentItem = `Syncing frame ${frameId}`;

    try {
      // Get pending sync items for this frame only
      const frameItems = await getPendingSyncItemsForFrame(frameId, 50);
      progress.totalItems = frameItems.length;
      progress.currentItem = `Syncing frame ${frameId} (${frameItems.length} items)`;

      // Update sync state manager with initial progress
      await this.updateSyncState(progress);

      // Process frame items in parallel batches with frame context
      await this.processBatchedItems(frameItems, progress, frameId);

      progress.isProcessing = false;
      progress.currentItem = `Frame ${frameId} sync completed`;

      // Update sync state manager with final progress
      await this.updateSyncState(progress);

      return progress;

    } catch (error) {
      progress.isProcessing = false;
      progress.currentItem = `Frame ${frameId} sync failed`;
      await this.updateSyncState(progress);
      throw error;
    }
  }

  /**
   * Process the sync queue - main entry point for sync operations
   */
  async processSyncQueue(): Promise<SyncProgress> {
    if (this.isProcessing && this.currentSyncPromise) {
      console.log('[ReactiveSyncService] Global sync already in progress, sharing current operation');
      return await this.currentSyncPromise;
    }

    console.log('[ReactiveSyncService] Starting global sync');

    this.isProcessing = true;
    this.abortController = new AbortController();

    // Create and store the promise so concurrent calls can share it
    this.currentSyncPromise = this.executeGlobalSync();
    
    try {
      const result = await this.currentSyncPromise;
      return result;
    } finally {
      this.isProcessing = false;
      this.abortController = null;
      this.currentSyncPromise = null;
    }
  }

  /**
   * Execute global sync operation (internal method)
   */
  private async executeGlobalSync(): Promise<SyncProgress> {
    const progress = this.createInitialProgress();

    try {
      // Get pending sync items
      const pendingItems = await getPendingSyncItems(50); // Process up to 50 items
      progress.totalItems = pendingItems.length;

      // Update sync state manager with initial progress
      await this.updateSyncState(progress);

      // Process items in parallel batches
      await this.processBatchedItems(pendingItems, progress);

      progress.isProcessing = false;
      progress.currentItem = undefined;

      // Update sync state manager with final progress
      await this.updateSyncState(progress);

      return progress;

    } catch (error) {
      progress.isProcessing = false;
      progress.currentItem = undefined;
      await this.updateSyncState(progress);
      throw error;
    }
  }

  /**
   * Process items in parallel batches for optimal performance
   */
  private async processBatchedItems(
    items: SyncQueueItem[], 
    progress: SyncProgress,
    frameId?: string
  ): Promise<void> {
    const batchSize = Math.min(this.DEFAULT_BATCH_SIZE, this.MAX_BATCH_SIZE);
    let consecutiveFailures = 0;
    const maxConsecutiveFailures = 2;
    const totalBatches = Math.ceil(items.length / batchSize);
    const batchStartTimes: number[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      if (this.abortController?.signal.aborted) {
        break;
      }

      const batch = items.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const batchStartTime = Date.now();
      batchStartTimes.push(batchStartTime);
      
      // Update batch-aware progress information
      const framePrefix = frameId ? `Frame ${frameId}: ` : '';
      progress.currentItem = `${framePrefix}Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`;
      progress.batchInfo = {
        currentBatch: batchNumber,
        totalBatches,
        batchSize: batch.length,
        processingMode: 'parallel',
        batchItemsProcessed: 0,
        batchItemsTotal: batch.length
      };
      
      this.updatePerformanceMetrics(progress);
      
      // Update sync state with real-time progress
      await this.updateSyncState(progress);

      try {
        // Process batch in parallel using Promise.allSettled
        const batchResults = await Promise.allSettled(
          batch.map(item => this.processSyncItemWithErrorHandling(item))
        );

        // Process batch results
        await this.processBatchResults(batch, batchResults, progress);
        consecutiveFailures = 0; // Reset failure counter on success
        
        // Update batch completion metrics
        const batchEndTime = Date.now();
        const batchDuration = batchEndTime - batchStartTime;
        if (progress.performanceMetrics) {
          progress.performanceMetrics.averageBatchTime = 
            batchStartTimes.length > 0 
              ? batchStartTimes.reduce((acc, _start, idx) => 
                  acc + (idx < batchStartTimes.length - 1 ? 0 : batchDuration), 0) / batchStartTimes.length
              : batchDuration;
        }
        
      } catch (batchError) {
        consecutiveFailures++;
        console.warn(`Batch ${batchNumber} failed (consecutive failures: ${consecutiveFailures}):`, batchError);
        
        if (consecutiveFailures >= maxConsecutiveFailures) {
          console.warn('Too many consecutive batch failures, switching to sequential mode for remaining items');
          // Update processing mode
          if (progress.batchInfo) {
            progress.batchInfo.processingMode = 'sequential';
          }
          // Process remaining items sequentially
          await this.processBatchSequentially(items.slice(i), progress, frameId);
          break;
        } else {
          // Fall back to sequential processing for this batch only
          await this.handleBatchFailure(batch, batchError as Error, progress);
        }
      }
      
      // Update progress
      progress.processedItems += batch.length;
      if (progress.batchInfo) {
        progress.batchInfo.batchItemsProcessed = batch.length;
      }
      this.updatePerformanceMetrics(progress);
      
      // Update sync state with progress
      await this.updateSyncState(progress);

      // Adaptive delay between batches based on performance
      if (i + batchSize < items.length) {
        const delay = consecutiveFailures > 0 ? 500 : 200; // Longer delay after failures
        await this.delay(delay);
      }
    }
  }

  /**
   * Update performance metrics for progress reporting
   */
  private updatePerformanceMetrics(progress: SyncProgress): void {
    if (!progress.performanceMetrics) return;
    
    const now = Date.now();
    const elapsed = now - progress.performanceMetrics.startTime;
    const elapsedSeconds = elapsed / 1000;
    
    // Calculate items per second
    if (elapsedSeconds > 0) {
      progress.performanceMetrics.itemsPerSecond = progress.processedItems / elapsedSeconds;
    }
    
    // Estimate time remaining
    const remainingItems = progress.totalItems - progress.processedItems;
    if (progress.performanceMetrics.itemsPerSecond > 0) {
      progress.performanceMetrics.estimatedTimeRemaining = 
        remainingItems / progress.performanceMetrics.itemsPerSecond;
    }
  }

  /**
   * Process remaining items sequentially as fallback
   */
  private async processBatchSequentially(
    items: SyncQueueItem[],
    progress: SyncProgress,
    frameId?: string
  ): Promise<void> {
    for (const item of items) {
      if (this.abortController?.signal.aborted) {
        break;
      }

      const framePrefix = frameId ? `Frame ${frameId}: ` : '';
      progress.currentItem = `${framePrefix}${item.objectType}: ${item.objectId} (sequential mode)`;
      
      // Update sync state with progress
      await this.updateSyncState(progress);

      try {
        const result = await this.processSyncItemWithErrorHandling(item);
        if (result.success) {
          progress.successfulItems++;
          await this.updateSyncItemStatus(item, 'completed');
        } else {
          progress.failedItems++;
          await this.handleSyncError(item, result.message);
        }
      } catch (error) {
        progress.failedItems++;
        await this.handleSyncError(item, error instanceof Error ? error.message : 'Unknown error');
      }

      progress.processedItems++;
      
      // Update sync state with progress
      await this.updateSyncState(progress);

      // Small delay between items in sequential mode
      await this.delay(100);
    }
  }

  /**
   * Process batch results and update sync statistics
   */
  private async processBatchResults(
    batch: SyncQueueItem[],
    results: PromiseSettledResult<SyncResult>[],
    progress: SyncProgress
  ): Promise<void> {
    const updatePromises: Promise<void>[] = [];

    for (let i = 0; i < batch.length; i++) {
      const item = batch[i];
      const result = results[i];

      if (result.status === 'fulfilled') {
        const syncResult = result.value;
        if (syncResult.success) {
          progress.successfulItems++;
          updatePromises.push(this.updateSyncItemStatus(item, 'completed'));
          
          // Update database sync status (this will also update enhanced sync state manager)
          console.log('[ReactiveSyncService] Sync successful, updating database:', {
            objectType: item.objectType,
            objectId: item.objectId,
            frameId: item.context?.frameId
          });
          
          if (item.objectType === 'capture') {
            const updatePromise = reactiveDatabase.updateCapture(item.objectId, { syncStatus: 'synced' })
              .then(() => {
                console.log('[ReactiveSyncService] Capture sync status updated successfully:', item.objectId);
              })
              .catch((error) => {
                console.error('[ReactiveSyncService] Error updating capture sync status:', error);
              });
            updatePromises.push(updatePromise);
          } else if (item.objectType === 'frame') {
            const updatePromise = reactiveDatabase.updateFrame(item.objectId, { syncStatus: 'synced' })
              .then(() => {
                console.log('[ReactiveSyncService] Frame sync status updated successfully:', item.objectId);
              })
              .catch((error) => {
                console.error('[ReactiveSyncService] Error updating frame sync status:', error);
              });
            updatePromises.push(updatePromise);
          }
        } else {
          progress.failedItems++;
          // Update error summary
          this.updateErrorSummary(progress, syncResult.message);
          updatePromises.push(this.handleSyncError(item, syncResult.message));
        }
      } else {
        // Promise was rejected
        progress.failedItems++;
        const errorMessage = result.reason instanceof Error ? result.reason.message : 'Unknown error';
        // Update error summary
        this.updateErrorSummary(progress, errorMessage);
        updatePromises.push(this.handleSyncError(item, errorMessage));
      }
    }

    // Execute all database updates in parallel
    await Promise.allSettled(updatePromises);
  }

  /**
   * Update error summary in progress tracking
   */
  private updateErrorSummary(progress: SyncProgress, errorMessage: string): void {
    if (!progress.errorSummary) return;
    
    const errorType = this.categorizeError(errorMessage);
    switch (errorType) {
      case 'network':
        progress.errorSummary.networkErrors++;
        break;
      case 'auth':
        progress.errorSummary.authErrors++;
        break;
      case 'server':
        progress.errorSummary.serverErrors++;
        break;
      case 'client':
        progress.errorSummary.clientErrors++;
        break;
      default:
        progress.errorSummary.unknownErrors++;
        break;
    }
  }

  /**
   * Process a single sync item with comprehensive error handling
   */
  private async processSyncItemWithErrorHandling(item: SyncQueueItem): Promise<SyncResult> {
    try {
      // Update status to processing
      await this.updateSyncItemStatus(item, 'processing');

      // Process the actual sync operation
      const result = await this.processSyncItem(item);
      return result;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
        objectId: item.objectId,
        objectType: item.objectType
      };
    }
  }

  /**
   * Process a single sync item
   */
  private async processSyncItem(item: SyncQueueItem): Promise<SyncResult> {
    if (item.objectType === 'frame') {
      return await this.syncFrame(item);
    } else if (item.objectType === 'capture') {
      return await this.syncCapture(item);
    } else {
      throw new Error(`Unknown object type: ${item.objectType}`);
    }
  }

  /**
   * Sync a single frame to the server
   */
  private async syncFrame(item: SyncQueueItem): Promise<SyncResult> {
    // Get frame data from IndexedDB
    const { getFrameById } = await import('@/lib/db/frameOperations');
    const frame = await getFrameById(item.objectId);

    if (!frame) {
      return {
        success: false,
        message: 'Frame not found in local database',
        objectId: item.objectId,
        objectType: 'frame'
      };
    }

    // Note: Authentication handled by authFetch automatically

    // Prepare sync request
    const syncRequest = {
      operation_type: item.operationType,
      object_type: 'frame',
      object_id: item.objectId,
      frame_data: {
        frameId: frame.frameId,
        modelNumber: frame.modelNumber,
        machineSerialNumber: frame.machineSerialNumber,
        inspectorName: frame.inspectorName,
        creationTimestamp: frame.creationTimestamp,
        lastModifiedTimestamp: frame.lastModifiedTimestamp,
        status: frame.status,
        captureCount: frame.captureCount,
        metadata: frame.metadata
      }
    };

    // Send to server using authenticated fetch
    const response = await authFetch('/api/v1/sync/frame', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(syncRequest),
      signal: this.abortController?.signal
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const result = await response.json();
    
    // Update local sync status if successful
    if (result.success) {
      // Use reactive database for immediate UI updates
      await reactiveDatabase.updateFrame(item.objectId, {
        syncStatus: 'synced',
        lastSyncedAt: Date.now()
      });
    }
    
    return {
      success: result.success,
      message: result.message,
      objectId: result.object_id,
      objectType: 'frame',
      serverObjectId: result.server_object_id
    };
  }

  /**
   * Sync a single capture to the server
   */
  private async syncCapture(item: SyncQueueItem): Promise<SyncResult> {
    // Get capture data from IndexedDB
    const { getCaptureById } = await import('@/lib/db/captureOperations');
    const capture = await getCaptureById(item.objectId);

    if (!capture) {
      return {
        success: false,
        message: 'Capture not found in local database',
        objectId: item.objectId,
        objectType: 'capture'
      };
    }

    // Note: Authentication handled by authFetch automatically

    // Use FormData for multipart upload to handle images
    const formData = new FormData();
    formData.append('operation_type', item.operationType);
    formData.append('object_type', 'capture');
    formData.append('object_id', item.objectId);
    formData.append('frame_id', capture.frameId);
    
    // Add capture metadata as JSON
    const captureData = {
      captureId: capture.captureId,
      frameId: capture.frameId,
      captureTimestamp: capture.captureTimestamp,
      detectionResults: capture.detectionResults
    };
    formData.append('capture_data', JSON.stringify(captureData));

    // Add image blobs if they exist
    if (capture.originalImageBlob) {
      formData.append('original_image', capture.originalImageBlob, 'original.jpg');
    }
    if (capture.processedImageBlob) {
      formData.append('processed_image', capture.processedImageBlob, 'processed.jpg');
    }
    if (capture.thumbnailBlob) {
      formData.append('thumbnail_image', capture.thumbnailBlob, 'thumbnail.jpg');
    }

    // Send to server using authenticated fetch (don't set Content-Type header for FormData)
    const response = await authFetch('/api/v1/sync/capture', {
      method: 'POST',
      headers: {
        // Content-Type will be set automatically for FormData with boundary
      },
      body: formData,
      signal: this.abortController?.signal
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`HTTP ${response.status}: ${error}`);
    }

    const result = await response.json();
    
    // Note: Database sync status will be updated by batch processing logic
    
    return {
      success: result.success,
      message: result.message,
      objectId: result.object_id,
      objectType: 'capture',
      serverObjectId: result.server_object_id
    };
  }

  /**
   * Handle sync errors with enhanced batch-aware retry logic
   */
  private async handleSyncError(item: SyncQueueItem, errorMessage: string): Promise<void> {
    const newAttemptCount = item.attemptCount + 1;

    // Analyze error type for intelligent retry strategy
    const errorType = this.categorizeError(errorMessage);
    const shouldRetry = this.shouldRetryBasedOnError(errorType, newAttemptCount);

    if (!shouldRetry || newAttemptCount >= this.MAX_RETRIES) {
      // Max retries reached or non-retryable error, mark as failed
      await updateSyncQueueItem({
        ...item,
        status: 'failed',
        attemptCount: newAttemptCount,
        lastAttempt: Date.now(),
        errorDetails: {
          message: errorMessage,
          errorType,
          finalAttempt: true
        }
      });
      console.error(`Sync failed after ${newAttemptCount} attempts (${errorType}):`, errorMessage, item);
    } else {
      // Schedule for retry with intelligent backoff based on error type
      const backoffDelay = this.calculateBackoffDelay(errorType, newAttemptCount);
      await updateSyncQueueItem({
        ...item,
        status: 'pending',
        attemptCount: newAttemptCount,
        lastAttempt: Date.now() + backoffDelay,
        errorDetails: {
          message: errorMessage,
          errorType,
          retryScheduled: true,
          nextRetryAt: Date.now() + backoffDelay
        }
      });
      console.warn(`Sync attempt ${newAttemptCount} failed (${errorType}), will retry in ${backoffDelay}ms:`, errorMessage);
    }
  }

  /**
   * Categorize error types for intelligent retry strategies
   */
  private categorizeError(errorMessage: string): SyncErrorType {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return 'network';
    }
    if (message.includes('unauthorized') || message.includes('auth') || message.includes('401')) {
      return 'auth';
    }
    if (message.includes('500') || message.includes('502') || message.includes('503') || message.includes('504')) {
      return 'server';
    }
    if (message.includes('400') || message.includes('404') || message.includes('422')) {
      return 'client';
    }
    return 'unknown';
  }

  /**
   * Determine if an error should be retried based on type and attempt count
   */
  private shouldRetryBasedOnError(errorType: string, attemptCount: number): boolean {
    switch (errorType) {
      case 'network':
        return attemptCount <= 5; // More retries for network issues
      case 'server':
        return attemptCount <= 4; // Retry server errors with backoff
      case 'auth':
        return attemptCount <= 2; // Limited retries for auth issues
      case 'client':
        return false; // Don't retry client errors (400, 404, etc.)
      case 'unknown':
        return attemptCount <= this.MAX_RETRIES;
      default:
        return attemptCount <= this.MAX_RETRIES;
    }
  }

  /**
   * Calculate intelligent backoff delay based on error type
   */
  private calculateBackoffDelay(errorType: string, attemptCount: number): number {
    const baseDelay = this.RETRY_DELAY;
    
    switch (errorType) {
      case 'network':
        // Longer delays for network issues
        return Math.min(baseDelay * Math.pow(2, attemptCount), 60000); // Max 60 seconds
      case 'server':
        // Moderate delays for server issues
        return Math.min(baseDelay * Math.pow(1.5, attemptCount), 30000); // Max 30 seconds
      case 'auth':
        // Short delays for auth issues (might be temporary token issue)
        return Math.min(baseDelay * attemptCount, 10000); // Max 10 seconds
      case 'unknown':
      default:
        // Standard exponential backoff
        return Math.min(baseDelay * Math.pow(2, attemptCount - 1), 30000); // Max 30 seconds
    }
  }

  /**
   * Handle batch processing failures with graceful degradation
   */
  private async handleBatchFailure(
    batch: SyncQueueItem[], 
    error: Error, 
    progress: SyncProgress
  ): Promise<void> {
    console.error('Batch processing failed, falling back to sequential processing:', error);
    
    // Fall back to sequential processing for this batch
    for (const item of batch) {
      if (this.abortController?.signal.aborted) {
        break;
      }

      try {
        const result = await this.processSyncItemWithErrorHandling(item);
        if (result.success) {
          progress.successfulItems++;
          await this.updateSyncItemStatus(item, 'completed');
        } else {
          progress.failedItems++;
          await this.handleSyncError(item, result.message);
        }
      } catch (sequentialError) {
        progress.failedItems++;
        await this.handleSyncError(item, sequentialError instanceof Error ? sequentialError.message : 'Unknown error');
      }
      
      progress.processedItems++;
    }
  }

  /**
   * Update sync item status
   */
  private async updateSyncItemStatus(
    item: SyncQueueItem, 
    status: 'pending' | 'processing' | 'completed' | 'failed'
  ): Promise<void> {
    const updatedItem = {
      ...item,
      status,
      lastAttempt: Date.now()
    };

    // If completed, remove from queue
    if (status === 'completed' && item.queueId) {
      await deleteSyncQueueItem(item.queueId);
    } else {
      // Update item status
      await updateSyncQueueItem(updatedItem);
    }
  }

  /**
   * Get sync statistics
   */
  async getSyncStats() {
    return await getSyncStats();
  }

  /**
   * Stop current sync process
   */
  stopSync(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  /**
   * Check if sync is currently processing
   */
  get isSyncing(): boolean {
    return this.isProcessing;
  }

  // Note: getAuthTokens method removed - authentication now handled by authFetch service

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry failed sync items
   */
  async retryFailedItems(): Promise<SyncProgress> {
    if (this.isProcessing && this.currentSyncPromise) {
      console.log('[ReactiveSyncService] Sync already in progress, will wait for completion before retry');
      await this.currentSyncPromise;
    }

    // Get failed items specifically
    const failedItems = await getFailedSyncItems();
    
    // Reset failed items to pending status
    for (const item of failedItems) {
      await updateSyncQueueItem({
        ...item,
        status: 'pending',
        attemptCount: 0, // Reset attempt count for retry
        lastAttempt: Date.now()
      });
    }

    // Process the queue again
    return await this.processSyncQueue();
  }
}

// Export singleton instance
export const reactiveSyncService = new ReactiveSyncService();

// Export class for testing
export { ReactiveSyncService };

// Re-export types for convenience
export type { SyncResult, SyncProgress, SyncErrorType } from './types';