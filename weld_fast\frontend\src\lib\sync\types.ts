'use client';

/**
 * Shared sync type definitions
 * 
 * This file contains all the type definitions used across the reactive sync system
 * to ensure consistency and eliminate duplication.
 */

// Core sync statistics
export interface SyncStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

// Sync progress with enhanced batch-aware reporting
export interface SyncProgress {
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  currentItem?: string;
  isProcessing: boolean;
  // Enhanced batch-aware progress reporting
  batchInfo?: {
    currentBatch: number;
    totalBatches: number;
    batchSize: number;
    processingMode: 'parallel' | 'sequential';
    batchItemsProcessed: number;
    batchItemsTotal: number;
  };
  performanceMetrics?: {
    itemsPerSecond: number;
    averageBatchTime: number;
    estimatedTimeRemaining: number;
    startTime: number;
  };
  errorSummary?: {
    networkErrors: number;
    authErrors: number;
    serverErrors: number;
    clientErrors: number;
    unknownErrors: number;
  };
}

// Individual sync operation result
export interface SyncResult {
  success: boolean;
  message: string;
  objectId: string;
  objectType: 'frame' | 'capture';
  serverObjectId?: string;
}

// Item-level change tracking
export interface ItemUpdate {
  frameId: string;
  itemId: string;
  itemType: 'frame' | 'capture';
  syncStatus: 'synced' | 'pending' | 'conflict';
  timestamp: number;
}

// Frame-specific sync state
export interface FrameState {
  pending: number;
  synced: number;
  failed: number;
  lastUpdated: number;
}

// Enhanced sync state (complete state structure)
export interface EnhancedSyncState {
  // Global sync state
  stats: SyncStats;
  progress: SyncProgress | null;
  isProcessing: boolean;
  lastUpdated: number;
  
  // Item-level change tracking
  itemUpdates: ItemUpdate[];
  
  // Frame-specific state
  frameStates: Map<string, FrameState>;
}

// Sync status type union
export type SyncStatus = 'synced' | 'pending' | 'conflict';

// Object type union
export type SyncObjectType = 'frame' | 'capture';

// Processing mode type union
export type ProcessingMode = 'parallel' | 'sequential';

// Error type categorization
export type SyncErrorType = 'network' | 'auth' | 'server' | 'client' | 'unknown';