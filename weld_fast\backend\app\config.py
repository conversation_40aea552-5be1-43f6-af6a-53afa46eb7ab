"""
Configuration settings for the weld defect detection system
"""

import os
import json
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Database settings
    database_url: str = Field(default="sqlite+aiosqlite:///./weld_detection.db")
    
    # Storage settings
    storage_type: str = Field(default="database")
    
    # Image processing settings
    image_compression_enabled: bool = Field(default=True)
    image_compression_quality: int = Field(default=85)
    max_image_size_mb: int = Field(default=10)
    
    # Thumbnail settings
    thumbnail_small_size: int = Field(default=150)
    thumbnail_medium_size: int = Field(default=300)
    thumbnail_large_size: int = Field(default=600)
    
    # Performance settings
    enable_storage_metrics: bool = Field(default=True)
    storage_health_check_interval: int = Field(default=300)
    
    # Security settings
    secret_key: str = Field(default="dev-secret-key-for-development-only-32-chars")  # Development default
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    
    # API settings
    api_title: str = Field(default="Weld Defect Detection API")
    api_version: str = Field(default="1.0.0")
    api_description: str = Field(default="Backend API for weld defect detection system")
    
    # CORS settings
    allowed_origins: List[str] = Field(default=["http://localhost:3000", "http://localhost:3001"])
    
    # Email settings (optional)
    smtp_host: Optional[str] = Field(default=None)
    smtp_port: Optional[int] = Field(default=587)
    smtp_username: Optional[str] = Field(default=None)
    smtp_password: Optional[str] = Field(default=None)
    
    # Frontend URL for email links
    frontend_url: str = Field(default="http://localhost:3000")
    
    # Logging settings
    log_level: str = Field(default="INFO")
    log_format: str = Field(default="text")
    
    # Performance settings
    workers: int = Field(default=1)
    max_connections: int = Field(default=100)
    keepalive_timeout: int = Field(default=5)
    
    @validator('allowed_origins', pre=True)
    def parse_allowed_origins(cls, v):
        """Parse ALLOWED_ORIGINS from JSON string or list"""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # If it's a single URL as string, convert to list
                return [v]
        return v
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        """Ensure secret key is strong enough"""
        if not v:
            raise ValueError("SECRET_KEY is required")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        
        # Warn if using development default in production-like environment
        if v == "dev-secret-key-for-development-only-32-chars":
            import os
            if os.getenv("NODE_ENV") == "production" or os.getenv("ENVIRONMENT") == "production":
                raise ValueError("Development SECRET_KEY cannot be used in production. Set a secure SECRET_KEY environment variable.")
        
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'


# Global settings instance
settings = Settings()