'use client';

import { useState, useEffect } from 'react';
import { BarChart3, Loader2 } from 'lucide-react';
import { authFetchBlob } from '@/lib/auth/authenticatedFetch';

interface PDFReportButtonProps {
  frameId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary';
}

export function PDFReportButton({ 
  frameId, 
  className = '', 
  size = 'md', 
  variant = 'primary' 
}: PDFReportButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  // Track online/offline status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    // Set initial status
    updateOnlineStatus();

    // Listen for online/offline events
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const handleGenerateReport = async () => {
    if (isGenerating || !isOnline) return;

    setIsGenerating(true);
    try {
      const url = `/api/v1/reports/frame/${frameId}/pdf`;
      console.log('Requesting PDF report from:', url);
      
      // Use authenticated fetch with automatic token refresh
      const blob = await authFetchBlob(url);
      
      // Use default filename (we can't access response headers with authFetchBlob)
      const filename = `weld_inspection_report_${frameId}.pdf`;

      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

    } catch (error) {
      console.error('Error generating PDF report:', error);
      if (!isOnline) {
        alert('Cannot generate report while offline. Please check your connection and try again.');
      } else {
        alert('Failed to generate PDF report. Please try again.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const iconSizes = {
    sm: 12,
    md: 16,
    lg: 20
  };

  // Variant classes
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
  };

  const isDisabled = isGenerating || !isOnline;

  return (
    <button
      onClick={handleGenerateReport}
      disabled={isDisabled}
      title={!isOnline ? 'Report generation unavailable offline' : isGenerating ? 'Generating report...' : 'Generate PDF report'}
      className={`
        flex items-center gap-2 rounded transition-colors
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      {isGenerating ? (
        <Loader2 size={iconSizes[size]} className="animate-spin" />
      ) : (
        <BarChart3 size={iconSizes[size]} />
      )}
      {isGenerating ? 'Generating...' : 'Report'}
    </button>
  );
}