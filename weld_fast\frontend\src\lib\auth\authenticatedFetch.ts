'use client';

/**
 * Centralized Authenticated Fetch Service
 * 
 * Provides a single API for all authenticated requests with automatic token refresh.
 * Replaces direct fetch calls throughout the application to ensure consistent
 * auth handling and token renewal.
 */

interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

interface AuthenticatedFetchOptions extends RequestInit {
  skipAuthRefresh?: boolean; // Skip auth refresh to prevent infinite loops
}

class AuthenticatedFetchService {
  private readonly API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
  private isRefreshing = false;
  private refreshPromise: Promise<boolean> | null = null;

  /**
   * Main authenticated fetch method with automatic token refresh
   */
  async authenticatedFetch(url: string, options: AuthenticatedFetchOptions = {}): Promise<Response> {
    const { skipAuthRefresh = false, ...fetchOptions } = options;

    // Get current tokens
    let tokens = this.getAuthTokens();
    if (!tokens) {
      throw new Error('Not authenticated. Please log in.');
    }

    // Check if token is expired and refresh if needed (unless explicitly skipped)
    if (!skipAuthRefresh && this.isTokenExpired()) {
      const refreshSuccess = await this.ensureTokenRefresh();
      if (!refreshSuccess) {
        throw new Error('Authentication expired. Please log in again.');
      }
      tokens = this.getAuthTokens();
      if (!tokens) {
        throw new Error('Token refresh failed. Please log in again.');
      }
    }

    // Make the authenticated request
    const fullUrl = url.startsWith('http') ? url : `${this.API_BASE}${url}`;
    const response = await fetch(fullUrl, {
      ...fetchOptions,
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        ...fetchOptions.headers,
      },
    });

    // Handle 401 responses with token refresh (unless explicitly skipped)
    if (response.status === 401 && !skipAuthRefresh) {
      console.log('[AuthenticatedFetch] Got 401, attempting token refresh...');
      
      const refreshSuccess = await this.ensureTokenRefresh();
      if (refreshSuccess) {
        const newTokens = this.getAuthTokens();
        if (newTokens) {
          console.log('[AuthenticatedFetch] Token refreshed, retrying request...');
          // Retry the original request with new token
          return fetch(fullUrl, {
            ...fetchOptions,
            headers: {
              'Authorization': `Bearer ${newTokens.access_token}`,
              ...fetchOptions.headers,
            },
          });
        }
      }
      
      // If refresh failed, throw authentication error
      throw new Error('Authentication expired. Please log in again.');
    }

    return response;
  }

  /**
   * Authenticated fetch with JSON response parsing
   */
  async fetchJson<T>(url: string, options: AuthenticatedFetchOptions = {}): Promise<T> {
    const response = await this.authenticatedFetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Authenticated fetch for blob responses (files, images, etc.)
   */
  async fetchBlob(url: string, options: AuthenticatedFetchOptions = {}): Promise<Blob> {
    const response = await this.authenticatedFetch(url, options);

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return response.blob();
  }

  /**
   * Get auth tokens from localStorage
   */
  private getAuthTokens(): AuthTokens | null {
    try {
      const stored = localStorage.getItem('auth_tokens');
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  /**
   * Check if the current token is expired or will expire soon
   */
  private isTokenExpired(): boolean {
    // Check if we have stored expiration time
    const storedAuth = localStorage.getItem('auth_tokens');
    if (storedAuth) {
      try {
        const parsed = JSON.parse(storedAuth);
        if (parsed.expires_at) {
          // Token expires in less than 5 minutes
          const expiresAt = parsed.expires_at;
          const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
          return expiresAt <= fiveMinutesFromNow;
        }
      } catch {
        // Fall through to default behavior
      }
    }

    // If no expires_at, assume it needs refresh (safe default)
    return true;
  }

  /**
   * Ensure token refresh happens only once at a time
   */
  private async ensureTokenRefresh(): Promise<boolean> {
    if (this.isRefreshing && this.refreshPromise) {
      // Wait for existing refresh to complete
      return await this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<boolean> {
    const tokens = this.getAuthTokens();
    if (!tokens?.refresh_token) {
      return false;
    }

    try {
      console.log('[AuthenticatedFetch] Refreshing tokens...');
      
      const response = await fetch(`${this.API_BASE}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: tokens.refresh_token,
        }),
      });

      if (!response.ok) {
        console.error('[AuthenticatedFetch] Token refresh failed:', response.status);
        return false;
      }

      const newTokens = await response.json();
      
      // Store new tokens with expiration time
      const tokenData = {
        ...newTokens,
        expires_at: Date.now() + (newTokens.expires_in * 1000)
      };

      localStorage.setItem('auth_tokens', JSON.stringify(tokenData));
      
      // Also update cookies for middleware
      document.cookie = `auth_tokens=${JSON.stringify(tokenData)}; path=/; secure; samesite=strict`;

      console.log('[AuthenticatedFetch] Tokens refreshed successfully');
      return true;
    } catch (error) {
      console.error('[AuthenticatedFetch] Token refresh error:', error);
      return false;
    }
  }

  /**
   * Clear authentication state (for logout)
   */
  clearAuth(): void {
    localStorage.removeItem('auth_tokens');
    localStorage.removeItem('auth_user');
    document.cookie = 'auth_tokens=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'auth_user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    const tokens = this.getAuthTokens();
    return !!tokens?.access_token;
  }
}

// Export singleton instance
export const authenticatedFetch = new AuthenticatedFetchService();

// Export convenience methods for easier usage
export const authFetch = authenticatedFetch.authenticatedFetch.bind(authenticatedFetch);
export const authFetchJson = authenticatedFetch.fetchJson.bind(authenticatedFetch);
export const authFetchBlob = authenticatedFetch.fetchBlob.bind(authenticatedFetch);