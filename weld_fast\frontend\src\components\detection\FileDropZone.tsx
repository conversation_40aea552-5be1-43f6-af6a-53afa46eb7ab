'use client';

import { useCallback, useState } from 'react';
import { Upload, FileImage, X, AlertCircle, Plus, FolderOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

const ACCEPTED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 20;

export interface UploadFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}

interface FileDropZoneProps {
  onFilesSelected: (files: UploadFile[]) => void;
  disabled?: boolean;
  selectedFiles: UploadFile[];
  onRemoveFile: (fileId: string) => void;
}

export default function FileDropZone({ 
  onFilesSelected, 
  disabled, 
  selectedFiles, 
  onRemoveFile 
}: FileDropZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const validateFile = (file: File): string | null => {
    if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
      return `${file.name}: Unsupported file type. Please use JPG, PNG, or WebP.`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `${file.name}: File too large. Maximum size is 10MB.`;
    }
    return null;
  };

  const processFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const newErrors: string[] = [];
    const validFiles: UploadFile[] = [];

    // Check total file count
    if (selectedFiles.length + fileArray.length > MAX_FILES) {
      newErrors.push(`Maximum ${MAX_FILES} files allowed. You can select ${MAX_FILES - selectedFiles.length} more files.`);
      setErrors(newErrors);
      return;
    }

    // Process each file
    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        newErrors.push(error);
        continue;
      }

      // Check if file already selected
      if (selectedFiles.some(f => f.file.name === file.name && f.file.size === file.size)) {
        newErrors.push(`${file.name}: File already selected.`);
        continue;
      }

      // Create preview URL
      const preview = URL.createObjectURL(file);
      
      validFiles.push({
        id: crypto.randomUUID(),
        file,
        preview,
        status: 'pending'
      });
    }

    setErrors(newErrors);
    
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  }, [selectedFiles, onFilesSelected]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFiles(files);
    }
  }, [disabled, processFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [processFiles]);

  return (
    <div className="w-full">
      {/* Upload Area */}
      <div className="mb-6">
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={cn(
            "relative border-2 border-dashed rounded-xl p-12 transition-all duration-200",
            isDragOver && !disabled
              ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20 scale-105"
              : "border-gray-300 dark:border-gray-600",
            disabled
              ? "opacity-50 cursor-not-allowed"
              : "hover:border-blue-400 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer"
          )}
        >
          <div className="text-center">
            <div className="mb-4">
              <Upload className={cn(
                "mx-auto h-16 w-16 transition-colors",
                isDragOver ? "text-blue-500" : "text-gray-400"
              )} />
            </div>
            <div className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {isDragOver ? 'Drop your images here' : 'Upload Images for Detection'}
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
              Drag and drop your weld images here, or click the button below to browse your files
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
              <button
                type="button"
                onClick={() => document.getElementById('file-input')?.click()}
                disabled={disabled}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                <FolderOpen className="h-5 w-5 mr-2" />
                Browse Files
              </button>
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                <span>or drag and drop</span>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-500 dark:text-gray-400 max-w-lg mx-auto">
              <div className="flex items-center justify-center space-x-2">
                <FileImage className="h-4 w-4" />
                <span>JPG, PNG, WebP</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <span>📏</span>
                <span>Max 10MB</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <span>📁</span>
                <span>{MAX_FILES - selectedFiles.length} remaining</span>
              </div>
            </div>
          </div>

          <input
            id="file-input"
            type="file"
            multiple
            accept={ACCEPTED_FILE_TYPES.join(',')}
            onChange={handleFileSelect}
            disabled={disabled}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
          />
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div className="space-y-1">
              {errors.map((error, index) => (
                <p key={index} className="text-sm text-red-700 dark:text-red-300">
                  {error}
                </p>
              ))}
            </div>
          </div>
          <button
            onClick={() => setErrors([])}
            className="mt-3 text-sm text-red-600 dark:text-red-400 hover:underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Selected Files Preview */}
      {selectedFiles.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <FileImage className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Ready for Processing
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedFiles.length} {selectedFiles.length === 1 ? 'image' : 'images'} selected
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                selectedFiles.forEach(file => {
                  URL.revokeObjectURL(file.preview);
                  onRemoveFile(file.id);
                });
              }}
              className="px-3 py-1.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
            >
              Clear All
            </button>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
            {selectedFiles.map((uploadFile) => (
              <div
                key={uploadFile.id}
                className="relative group bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="aspect-square relative">
                  <Image
                    src={uploadFile.preview}
                    alt={uploadFile.file.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                  />
                  
                  {/* Status overlay */}
                  {uploadFile.status !== 'pending' && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium text-white",
                        uploadFile.status === 'processing' && "bg-blue-500",
                        uploadFile.status === 'completed' && "bg-green-500",
                        uploadFile.status === 'error' && "bg-red-500"
                      )}>
                        {uploadFile.status === 'processing' && "Processing..."}
                        {uploadFile.status === 'completed' && "✓ Done"}
                        {uploadFile.status === 'error' && "✗ Error"}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* File info */}
                <div className="p-2">
                  <p className="text-xs font-medium text-gray-900 dark:text-white truncate">
                    {uploadFile.file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {(uploadFile.file.size / 1024 / 1024).toFixed(1)} MB
                  </p>
                </div>

                {/* Remove button */}
                <button
                  onClick={() => {
                    URL.revokeObjectURL(uploadFile.preview);
                    onRemoveFile(uploadFile.id);
                  }}
                  className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                >
                  <X className="h-3 w-3 text-white" />
                </button>
              </div>
            ))}
            
            {/* Add more button */}
            <button
              onClick={() => document.getElementById('file-input')?.click()}
              disabled={disabled || selectedFiles.length >= MAX_FILES}
              className="aspect-square border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus className="h-6 w-6 text-gray-400 mb-1" />
              <span className="text-xs text-gray-500 dark:text-gray-400">Add More</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}