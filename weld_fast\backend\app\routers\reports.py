from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Literal
import time
import io
import csv
import json
import asyncio

from ..database import get_db
from ..dependencies import get_current_user
from ..services.pdf_report_service import PDFReportService
from ..models.database import User

router = APIRouter(prefix="/api/v1/reports", tags=["reports"])


@router.get("/frame/{frame_id}/pdf")
async def generate_frame_pdf_report(
    frame_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Generate a PDF report comparing original and processed images for a frame."""
    try:
        pdf_service = PDFReportService(db)
        pdf_bytes = await pdf_service.generate_frame_comparison_report(frame_id)
        
        # Get frame summary for filename
        summary = await pdf_service.get_frame_summary(frame_id)
        filename = f"weld_inspection_{frame_id[:8]}_{int(time.time())}.pdf"
        
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate PDF report: {str(e)}"
        )


@router.get("/frame/{frame_id}/summary")
async def get_frame_summary(
    frame_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get summary information for a frame."""
    try:
        pdf_service = PDFReportService(db)
        summary = await pdf_service.get_frame_summary(frame_id)
        
        if not summary:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Frame {frame_id} not found"
            )
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get frame summary: {str(e)}"
        )


@router.get("/health")
async def reports_health_check():
    """Health check for reports service."""
    return {
        "status": "healthy",
        "service": "reports",
        "timestamp": int(time.time() * 1000)
    }


