from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import json

from ..models.database import Frame, Capture, User


class AnalyticsService:
    """Simple analytics service for generating reports from detection data."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_detection_summary(
        self, 
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get overall detection statistics summary."""
        
        # Simple count queries
        frame_query = select(func.count(Frame.frame_id))
        capture_query = select(func.count(Capture.capture_id))
        
        # Apply filters if needed
        if inspector_id or machine_serial or start_date or end_date:
            if inspector_id:
                frame_query = frame_query.filter(Frame.inspector_id == inspector_id)
            if machine_serial:
                frame_query = frame_query.filter(Frame.machine_serial_number == machine_serial)
            if start_date:
                frame_query = frame_query.filter(Frame.creation_timestamp >= start_date)
            if end_date:
                frame_query = frame_query.filter(Frame.creation_timestamp <= end_date)
            
            # For captures, need to join with frames for filtering
            if inspector_id or machine_serial or start_date or end_date:
                capture_query = select(func.count(Capture.capture_id)).select_from(
                    Capture.__table__.join(Frame.__table__, Capture.frame_id == Frame.frame_id)
                )
                if inspector_id:
                    capture_query = capture_query.filter(Frame.inspector_id == inspector_id)
                if machine_serial:
                    capture_query = capture_query.filter(Frame.machine_serial_number == machine_serial)
                if start_date:
                    capture_query = capture_query.filter(Frame.creation_timestamp >= start_date)
                if end_date:
                    capture_query = capture_query.filter(Frame.creation_timestamp <= end_date)
        
        # Execute queries
        frame_result = await self.db.execute(frame_query)
        capture_result = await self.db.execute(capture_query)
        
        total_frames = frame_result.scalar() or 0
        total_captures = capture_result.scalar() or 0
        
        # For detections, we'll get a simple estimate
        # In a real implementation, you'd query the JSON data properly
        total_detections = total_captures * 2  # Estimate 2 detections per capture
        avg_detections_per_capture = 2.0 if total_captures > 0 else 0
        
        return {
            'total_frames': total_frames,
            'total_captures': total_captures,
            'total_detections': total_detections,
            'avg_detections_per_capture': avg_detections_per_capture
        }
    
    async def get_detection_class_distribution(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get distribution of detected object classes."""
        
        # For now, return mock data
        # In a real implementation, you'd parse the JSON detection_results
        return [
            {'class': 'weld_defect', 'count': 15, 'avg_confidence': 0.85},
            {'class': 'crack', 'count': 8, 'avg_confidence': 0.78},
            {'class': 'porosity', 'count': 5, 'avg_confidence': 0.82},
            {'class': 'undercut', 'count': 3, 'avg_confidence': 0.75}
        ]
    
    async def get_detection_trends(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None,
        granularity: str = 'daily'
    ) -> List[Dict[str, Any]]:
        """Get detection trends over time."""
        
        # For now, return mock trend data
        # In a real implementation, you'd group by time periods
        import time
        current_time = int(time.time() * 1000)
        day_ms = 24 * 60 * 60 * 1000
        
        trends = []
        for i in range(7):  # Last 7 days
            timestamp = current_time - (i * day_ms)
            trends.append({
                'timestamp': timestamp,
                'date': datetime.fromtimestamp(timestamp / 1000, timezone.utc).isoformat(),
                'capture_count': 5 - i if i < 5 else 1,
                'detection_count': (5 - i) * 2 if i < 5 else 2
            })
        
        trends.reverse()  # Oldest first
        return trends
    
    async def get_inspector_performance(
        self,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get performance metrics by inspector."""
        
        # Query users and their frame counts
        query = select(
            User.user_id,
            User.full_name,
            func.count(Frame.frame_id).label('total_frames')
        ).select_from(
            User.__table__.outerjoin(Frame.__table__, User.user_id == Frame.inspector_id)
        ).group_by(User.user_id, User.full_name)
        
        result = await self.db.execute(query)
        users = result.all()
        
        performance = []
        for user in users:
            # Mock additional metrics
            performance.append({
                'inspector_id': user.user_id,
                'inspector_name': user.full_name,
                'total_frames': user.total_frames or 0,
                'total_captures': (user.total_frames or 0) * 3,  # Mock: 3 captures per frame
                'total_detections': (user.total_frames or 0) * 6,  # Mock: 6 detections per frame
                'avg_detections_per_capture': 2.0
            })
        
        return performance
    
    async def get_machine_statistics(
        self,
        inspector_id: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get statistics by machine serial number."""
        
        # Query unique machines
        query = select(
            Frame.machine_serial_number,
            Frame.model_number,
            func.count(Frame.frame_id).label('total_frames'),
            func.max(Frame.last_modified_timestamp).label('last_activity')
        ).group_by(Frame.machine_serial_number, Frame.model_number)
        
        if inspector_id:
            query = query.filter(Frame.inspector_id == inspector_id)
        if start_date:
            query = query.filter(Frame.creation_timestamp >= start_date)
        if end_date:
            query = query.filter(Frame.creation_timestamp <= end_date)
        
        result = await self.db.execute(query)
        machines = result.all()
        
        statistics = []
        for machine in machines:
            statistics.append({
                'machine_serial_number': machine.machine_serial_number,
                'model_number': machine.model_number,
                'total_frames': machine.total_frames,
                'total_captures': machine.total_frames * 3,  # Mock: 3 captures per frame
                'total_detections': machine.total_frames * 6,  # Mock: 6 detections per frame
                'last_activity': machine.last_activity,
                'last_activity_date': datetime.fromtimestamp(machine.last_activity / 1000, timezone.utc).isoformat()
            })
        
        return statistics
    
    async def get_sync_statistics(
        self,
        inspector_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get synchronization status statistics."""
        
        # Query frame sync status counts
        frame_query = select(
            Frame.sync_status,
            func.count().label('count')
        ).group_by(Frame.sync_status)
        
        if inspector_id:
            frame_query = frame_query.filter(Frame.inspector_id == inspector_id)
        
        frame_result = await self.db.execute(frame_query)
        frame_stats = {row.sync_status.value: row.count for row in frame_result.all()}
        
        # Query capture sync status counts
        capture_query = select(
            Capture.sync_status,
            func.count().label('count')
        ).group_by(Capture.sync_status)
        
        if inspector_id:
            capture_query = capture_query.select_from(
                Capture.__table__.join(Frame.__table__, Capture.frame_id == Frame.frame_id)
            ).filter(Frame.inspector_id == inspector_id)
        
        capture_result = await self.db.execute(capture_query)
        capture_stats = {row.sync_status.value: row.count for row in capture_result.all()}
        
        return {
            'frames': {
                'synced': frame_stats.get('synced', 0),
                'pending': frame_stats.get('pending', 0),
                'conflict': frame_stats.get('conflict', 0)
            },
            'captures': {
                'synced': capture_stats.get('synced', 0),
                'pending': capture_stats.get('pending', 0),
                'conflict': capture_stats.get('conflict', 0)
            }
        }
    
    async def get_confidence_analysis(
        self,
        inspector_id: Optional[str] = None,
        machine_serial: Optional[str] = None,
        start_date: Optional[int] = None,
        end_date: Optional[int] = None
    ) -> Dict[str, Any]:
        """Analyze detection confidence scores."""
        
        # For now, return mock confidence analysis
        # In a real implementation, you'd parse the JSON detection_results
        return {
            'total_detections': 50,
            'avg_confidence': 0.82,
            'min_confidence': 0.45,
            'max_confidence': 0.98,
            'confidence_ranges': {
                'high': 35,    # > 0.8
                'medium': 12,  # 0.5 - 0.8  
                'low': 3       # < 0.5
            }
        }