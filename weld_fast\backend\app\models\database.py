from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text, Float, JSON, Enum, ForeignKey, LargeBinary, Index, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import uuid
from datetime import datetime
from typing import Optional
import enum

Base = declarative_base()

class SyncStatus(str, enum.Enum):
    SYNCED = "synced"
    PENDING = "pending" 
    CONFLICT = "conflict"

class FrameStatus(str, enum.Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"

class OperationType(str, enum.Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"

class ObjectType(str, enum.Enum):
    FRAME = "frame"
    CAPTURE = "capture"

class QueueStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    INSPECTOR = "inspector"


class User(Base):
    __tablename__ = "users"
    
    user_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, nullable=False, index=True)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(Enum(UserRole), default=UserRole.INSPECTOR, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(Integer, nullable=False)
    last_login = Column(Integer, nullable=True)
    
    # Relationship to frames (inspector who created them)
    frames = relationship("Frame", back_populates="inspector", foreign_keys="Frame.inspector_id")


class Frame(Base):
    __tablename__ = "frames"
    
    frame_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    model_number = Column(String, nullable=False)
    machine_serial_number = Column(String, nullable=False)
    inspector_id = Column(String, ForeignKey("users.user_id"), nullable=False)
    inspector_name = Column(String, nullable=False)  # Keep for backward compatibility
    creation_timestamp = Column(Integer, nullable=False)
    last_modified_timestamp = Column(Integer, nullable=False)
    status = Column(Enum(FrameStatus), default=FrameStatus.ACTIVE, nullable=False)
    capture_count = Column(Integer, default=0, nullable=False)
    frame_metadata = Column(JSON, nullable=True)
    sync_status = Column(Enum(SyncStatus), default=SyncStatus.SYNCED, nullable=False)
    last_synced_at = Column(Integer, nullable=True)
    
    # Relationships
    inspector = relationship("User", back_populates="frames", foreign_keys=[inspector_id])
    captures = relationship("Capture", back_populates="frame", cascade="all, delete-orphan")


class Capture(Base):
    __tablename__ = "captures"
    
    capture_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    frame_id = Column(String, ForeignKey("frames.frame_id"), nullable=False)
    capture_timestamp = Column(Integer, nullable=False)
    original_image_blob = Column(LargeBinary, nullable=True)
    processed_image_blob = Column(LargeBinary, nullable=True)
    thumbnail_blob = Column(LargeBinary, nullable=True)
    detection_results = Column(JSON, nullable=False, default=list)
    sync_status = Column(Enum(SyncStatus), default=SyncStatus.SYNCED, nullable=False)
    sync_version = Column(Integer, default=1, nullable=False)
    last_sync_attempt = Column(Integer, nullable=True)
    
    # Relationship to frame
    frame = relationship("Frame", back_populates="captures")
    
    # Database indexes for performance optimization
    __table_args__ = (
        # Most common query: get captures by frame ordered by timestamp
        Index('idx_captures_frame_timestamp', 'frame_id', 'capture_timestamp'),
        
        # Sync operations: find captures by sync status
        Index('idx_captures_sync_status', 'sync_status'),
        
        # Conflict resolution: lookup by capture_id and sync_version
        Index('idx_captures_sync_version', 'capture_id', 'sync_version'),
        
        # Frame-based sync operations
        Index('idx_captures_frame_sync', 'frame_id', 'sync_status'),
        
        # Timestamp-based queries
        Index('idx_captures_timestamp', 'capture_timestamp'),
    )


class SyncQueueItem(Base):
    __tablename__ = "sync_queue"
    
    queue_id = Column(Integer, primary_key=True, autoincrement=True)
    operation_type = Column(Enum(OperationType), nullable=False)
    object_type = Column(Enum(ObjectType), nullable=False)
    object_id = Column(String, nullable=False)
    priority = Column(Integer, default=1, nullable=False)
    created_at = Column(Integer, nullable=False)
    attempt_count = Column(Integer, default=0, nullable=False)
    last_attempt = Column(Integer, nullable=True)
    status = Column(Enum(QueueStatus), default=QueueStatus.PENDING, nullable=False)