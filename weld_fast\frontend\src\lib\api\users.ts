import { User } from '@/context/AuthContext';
import { createAuthenticatedRequest } from './auth';

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
}

export interface UserUpdateData {
  username?: string;
  email?: string;
  full_name?: string;
  role?: 'admin' | 'inspector';
  is_active?: boolean;
}

export interface UserListParams {
  page?: number;
  page_size?: number;
  role?: 'admin' | 'inspector';
  active_only?: boolean;
}

export const usersApi = {
  // Get current user profile
  getProfile: async (): Promise<User> => {
    const request = createAuthenticatedRequest();
    return request<User>('/api/v1/users/me');
  },

  // Update current user profile
  updateProfile: async (data: UserUpdateData): Promise<User> => {
    const request = createAuthenticatedRequest();
    return request<User>('/api/v1/users/me', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Admin functions
  listUsers: async (
    params: UserListParams = {}
  ): Promise<UserListResponse> => {
    const request = createAuthenticatedRequest();
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.page_size) searchParams.set('page_size', params.page_size.toString());
    if (params.role) searchParams.set('role', params.role);
    if (params.active_only !== undefined) searchParams.set('active_only', params.active_only.toString());

    const url = `/api/v1/users/?${searchParams.toString()}`;
    return request<UserListResponse>(url);
  },

  // Get user by ID (admin only)
  getUserById: async (userId: string): Promise<User> => {
    const request = createAuthenticatedRequest();
    return request<User>(`/api/v1/users/${userId}`);
  },

  // Update user by ID (admin only)
  updateUserById: async (
    userId: string,
    data: UserUpdateData
  ): Promise<User> => {
    const request = createAuthenticatedRequest();
    return request<User>(`/api/v1/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete user by ID (admin only)
  deleteUserById: async (
    userId: string
  ): Promise<{ message: string }> => {
    const request = createAuthenticatedRequest();
    return request<{ message: string }>(`/api/v1/users/${userId}`, {
      method: 'DELETE',
    });
  },

  // Deactivate user (admin only)
  deactivateUser: async (
    userId: string
  ): Promise<{ message: string }> => {
    const request = createAuthenticatedRequest();
    return request<{ message: string }>(`/api/v1/users/${userId}/deactivate`, {
      method: 'POST',
    });
  },
};