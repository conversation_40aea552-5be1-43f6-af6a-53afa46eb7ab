'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Frame } from '@/lib/db/types';
// Enhanced session querying capabilities available but using basic load for now
import { loadSessionsWithFallback } from '@/lib/services/sessionSync';
import { useOrchestratedRefresh, REFRESH_PRESETS, broadcastRefreshTrigger } from '@/hooks/useOrchestratedRefresh';
import SessionCard from './SessionCard';

interface SessionListProps {
  onSessionSelect?: (frame: Frame) => void;
}

export default function SessionList({ onSessionSelect }: SessionListProps) {
  const router = useRouter();
  const [sessions, setSessions] = useState<Frame[]>([]);
  const [filteredSessions, setFilteredSessions] = useState<Frame[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'completed' | 'archived'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'captures'>('newest');

  const loadSessions = useCallback(async () => {
    try {
      // Use different loading states based on whether we have data
      const hasExistingData = sessions.length > 0;
      if (!hasExistingData) {
        setLoading(true);
      }
      setError(null);
      
      // Use enhanced loading with server fallback
      const frames = await loadSessionsWithFallback();
      setSessions(frames);
    } catch (err) {
      console.error('Error loading sessions:', err);
      setError('Failed to load sessions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  }, [sessions.length]);

  const filterAndSortSessions = useCallback(() => {
    let filtered = [...sessions];

    // Apply search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(session => 
        session.modelNumber.toLowerCase().includes(search) ||
        session.machineSerialNumber.toLowerCase().includes(search) ||
        session.inspectorName.toLowerCase().includes(search)
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(session => session.status === filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return a.creationTimestamp - b.creationTimestamp;
        case 'captures':
          return b.captureCount - a.captureCount;
        case 'newest':
        default:
          return b.creationTimestamp - a.creationTimestamp;
      }
    });

    setFilteredSessions(filtered);
  }, [sessions, searchTerm, filterStatus, sortBy]);

  // Orchestrated refresh system for automatic sync status updates
  const {
    refresh: refreshSessions
  } = useOrchestratedRefresh(loadSessions, {
    key: 'load-sessions',
    ...REFRESH_PRESETS.AUTO_REFRESH,
    triggers: {
      windowFocus: true,
      syncEvents: true,     // Auto-refresh when sync completes
      crossTab: true,       // Update when other tabs make changes
      manual: true
    },
    broadcastChannel: 'session-updates',
    onRefreshComplete: () => {
      console.log('Session list refreshed after sync completion');
    }
  });

  // Initial load
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  useEffect(() => {
    filterAndSortSessions();
  }, [filterAndSortSessions]);

  const handleSessionClick = (session: Frame) => {
    if (onSessionSelect) {
      onSessionSelect(session);
    } else {
      router.push(`/detection?frameId=${session.frameId}`);
    }
  };

  const handleRefresh = () => {
    // Use orchestrated refresh for manual refresh to prevent conflicts
    refreshSessions('user-action');
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center py-8">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></div>
          <span className="text-gray-600 dark:text-gray-300">Loading sessions...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center py-8">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.996-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Previous Sessions
        </h2>
        <button
          onClick={handleRefresh}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          title="Refresh sessions"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4 mb-6">
        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search sessions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <svg 
            className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4">
          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'completed' | 'archived')}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="archived">Archived</option>
          </select>

          {/* Sort By */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'captures')}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="captures">Most Captures</option>
          </select>
        </div>
      </div>

      {/* Sessions List */}
      {filteredSessions.length === 0 ? (
        <div className="text-center py-12">
          {sessions.length === 0 ? (
            <>
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No sessions yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Create your first detection session to get started
              </p>
            </>
          ) : (
            <>
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No matching sessions
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search or filter criteria
              </p>
            </>
          )}
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredSessions.map((session) => (
            <SessionCard
              key={session.frameId}
              session={session}
              onClick={() => handleSessionClick(session)}
              onDelete={(frameId) => {
                setSessions(prev => prev.filter(s => s.frameId !== frameId));
                // Broadcast session update to other components and tabs
                broadcastRefreshTrigger('session-updates', {
                  type: 'session-deleted',
                  frameId,
                  timestamp: Date.now()
                });
              }}
            />
          ))}
        </div>
      )}

      {filteredSessions.length > 0 && (
        <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
          Showing {filteredSessions.length} of {sessions.length} sessions
        </div>
      )}
    </div>
  );
}