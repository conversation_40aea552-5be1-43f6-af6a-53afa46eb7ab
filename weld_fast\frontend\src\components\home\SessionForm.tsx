'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from '@/context/SessionContext';
import { createFrame } from '@/lib/db/frameOperations';

interface FormData {
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
}

interface FormErrors {
  modelNumber?: string;
  machineSerialNumber?: string;
  inspectorName?: string;
  general?: string;
}

export default function SessionForm() {
  const router = useRouter();
  const { setSessionInfo } = useSession();
  const [formData, setFormData] = useState<FormData>({
    modelNumber: '',
    machineSerialNumber: '',
    inspectorName: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Model Number validation
    if (!formData.modelNumber.trim()) {
      newErrors.modelNumber = 'Model number is required';
    } else if (formData.modelNumber.trim().length < 2) {
      newErrors.modelNumber = 'Model number must be at least 2 characters';
    }

    // Machine Serial Number validation
    if (!formData.machineSerialNumber.trim()) {
      newErrors.machineSerialNumber = 'Machine serial number is required';
    } else if (formData.machineSerialNumber.trim().length < 3) {
      newErrors.machineSerialNumber = 'Machine serial number must be at least 3 characters';
    }

    // Inspector Name validation
    if (!formData.inspectorName.trim()) {
      newErrors.inspectorName = 'Inspector name is required';
    } else if (formData.inspectorName.trim().length < 2) {
      newErrors.inspectorName = 'Inspector name must be at least 2 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Create frame in IndexedDB
      const frame = await createFrame({
        modelNumber: formData.modelNumber.trim(),
        machineSerialNumber: formData.machineSerialNumber.trim(),
        inspectorName: formData.inspectorName.trim(),
        status: 'active',
        captureCount: 0,
        metadata: {
          createdFromHome: true,
          userAgent: navigator.userAgent
        }
      });

      // Update session context
      setSessionInfo({
        modelNumber: formData.modelNumber.trim(),
        machineSerialNumber: formData.machineSerialNumber.trim(),
        inspectorName: formData.inspectorName.trim(),
        frameId: frame.frameId
      });

      // Navigate to detection page with frame ID
      router.push(`/detection?frameId=${frame.frameId}`);
    } catch (error) {
      console.error('Error creating session:', error);
      setErrors({ general: 'Failed to create session. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Weld Defect Detection
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Enter session details to start detection
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Model Number */}
          <div>
            <label 
              htmlFor="modelNumber" 
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Model Number (Type of Frame)
            </label>
            <input
              id="modelNumber"
              type="text"
              value={formData.modelNumber}
              onChange={(e) => handleInputChange('modelNumber', e.target.value)}
              className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                errors.modelNumber 
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
              placeholder="e.g., WF-2024A"
              disabled={isSubmitting}
            />
            {errors.modelNumber && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.modelNumber}
              </p>
            )}
          </div>

          {/* Machine Serial Number */}
          <div>
            <label 
              htmlFor="machineSerialNumber" 
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Machine Serial Number
            </label>
            <input
              id="machineSerialNumber"
              type="text"
              value={formData.machineSerialNumber}
              onChange={(e) => handleInputChange('machineSerialNumber', e.target.value)}
              className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                errors.machineSerialNumber 
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
              placeholder="e.g., MSN-789456"
              disabled={isSubmitting}
            />
            {errors.machineSerialNumber && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.machineSerialNumber}
              </p>
            )}
          </div>

          {/* Inspector Name */}
          <div>
            <label 
              htmlFor="inspectorName" 
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Inspector Name
            </label>
            <input
              id="inspectorName"
              type="text"
              value={formData.inspectorName}
              onChange={(e) => handleInputChange('inspectorName', e.target.value)}
              className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                errors.inspectorName 
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                  : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2`}
              placeholder="e.g., John Smith"
              disabled={isSubmitting}
            />
            {errors.inspectorName && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.inspectorName}
              </p>
            )}
          </div>

          {/* General Error */}
          {errors.general && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-sm text-red-600 dark:text-red-400">
                {errors.general}
              </p>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-colors ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 focus:bg-blue-700'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Creating Session...
              </div>
            ) : (
              'Start Detection Session'
            )}
          </button>
        </form>
      </div>
    </div>
  );
}