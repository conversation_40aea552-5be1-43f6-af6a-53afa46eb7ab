// src/lib/detection/modelLoader.ts
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-webgl';

export interface ModelConfig {
  modelName: string;
  inputShape: number[];
  modelUrl: string;
  inputSize: number;
}

export interface LoadingProgress {
  loaded: number;
  total: number;
  progress: number; // 0-1
  status: 'loading' | 'complete' | 'error';
}

export class ModelLoader {
  private modelCache: Map<string, tf.GraphModel> = new Map();
  private loadingPromises: Map<string, Promise<tf.GraphModel>> = new Map();

  constructor() {
    this.initializeTensorFlow();
  }

  private async initializeTensorFlow(): Promise<void> {
    try {
      // Set backend to WebGL for better performance
      await tf.setBackend('webgl');
      await tf.ready();
      console.log('TensorFlow.js initialized with WebGL backend');
    } catch (error) {
      console.warn('Failed to initialize WebGL backend, falling back to CPU:', error);
      await tf.setBackend('cpu');
      await tf.ready();
      console.log('TensorFlow.js initialized with CPU backend');
    }
  }

  /**
   * Load a model from URL with progress tracking
   */
  async loadModel(
    modelName: string,
    onProgress?: (progress: LoadingProgress) => void
  ): Promise<tf.GraphModel> {
    // Check cache first
    if (this.modelCache.has(modelName)) {
      const cachedModel = this.modelCache.get(modelName)!;
      onProgress?.({
        loaded: 1,
        total: 1,
        progress: 1,
        status: 'complete'
      });
      return cachedModel;
    }

    // Check if already loading
    if (this.loadingPromises.has(modelName)) {
      return this.loadingPromises.get(modelName)!;
    }

    // Start loading
    const loadingPromise = this.loadModelInternal(modelName, onProgress);
    this.loadingPromises.set(modelName, loadingPromise);

    try {
      const model = await loadingPromise;
      this.modelCache.set(modelName, model);
      return model;
    } finally {
      this.loadingPromises.delete(modelName);
    }
  }

  private async loadModelInternal(
    modelName: string,
    onProgress?: (progress: LoadingProgress) => void
  ): Promise<tf.GraphModel> {
    const config = this.getModelConfig(modelName);
    
    try {
      onProgress?.({
        loaded: 0,
        total: 1,
        progress: 0,
        status: 'loading'
      });

      // Create a custom loading handler to track progress
      const model = await tf.loadGraphModel(config.modelUrl, {
        onProgress: (fraction) => {
          onProgress?.({
            loaded: fraction,
            total: 1,
            progress: fraction,
            status: 'loading'
          });
        }
      });

      console.log(`Model ${modelName} loaded successfully`);
      console.log('Model inputs:', model.inputs);
      console.log('Model outputs:', model.outputs);

      onProgress?.({
        loaded: 1,
        total: 1,
        progress: 1,
        status: 'complete'
      });

      return model;
    } catch (error) {
      console.error(`Failed to load model ${modelName}:`, error);
      onProgress?.({
        loaded: 0,
        total: 1,
        progress: 0,
        status: 'error'
      });
      throw error;
    }
  }

  /**
   * Warm up model with dummy tensor to ensure it's ready for inference
   */
  async warmupModel(model: tf.GraphModel, inputSize: number = 640): Promise<void> {
    try {
      console.log('Warming up model...');
      
      // Create dummy input tensor
      const dummyInput = tf.zeros([1, inputSize, inputSize, 3]);
      
      // Run inference
      const startTime = performance.now();
      const output = model.predict(dummyInput) as tf.Tensor;
      await output.data(); // Wait for computation to complete
      const endTime = performance.now();
      
      console.log(`Model warmup completed in ${endTime - startTime}ms`);
      
      // Clean up tensors
      dummyInput.dispose();
      output.dispose();
    } catch (error) {
      console.error('Model warmup failed:', error);
      throw error;
    }
  }

  /**
   * Get model configuration
   */
  private getModelConfig(modelName: string): ModelConfig {
    const configs: Record<string, ModelConfig> = {
      'yolov8n': {
        modelName: 'yolov8n',
        inputShape: [1, 640, 640, 3],
        modelUrl: '/models/yolov8n_web_model/model.json',
        inputSize: 640
      },
      'yolov8n-small': {
        modelName: 'yolov8n-small',
        inputShape: [1, 320, 320, 3],
        modelUrl: '/models/yolov8n_small_web_model/model.json',
        inputSize: 320
      }
    };

    const config = configs[modelName];
    if (!config) {
      throw new Error(`Unknown model: ${modelName}`);
    }

    return config;
  }

  /**
   * Get optimal model configuration based on device capabilities
   */
  getOptimalModelConfig(): { modelName: string; inputSize: number } {
    // Check device capabilities
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    const isLowEndDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;
    const hasLimitedMemory = (navigator as unknown as { deviceMemory?: number }).deviceMemory && (navigator as unknown as { deviceMemory?: number }).deviceMemory! <= 4;

    if (isMobile || isLowEndDevice || hasLimitedMemory) {
      return { modelName: 'yolov8n-small', inputSize: 320 };
    }

    return { modelName: 'yolov8n', inputSize: 640 };
  }

  /**
   * Dispose of cached models to free memory
   */
  dispose(): void {
    for (const model of this.modelCache.values()) {
      model.dispose();
    }
    this.modelCache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Get memory usage information
   */
  getMemoryInfo(): { numTensors: number; numBytes: number } {
    return tf.memory();
  }
}

// Singleton instance
export const modelService = new ModelLoader();