// src/components/detection/CaptureDetails.tsx
'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { ArrowLeft, Eye, EyeOff} from 'lucide-react';
import { getCaptureById } from '@/lib/db/captureOperations';
import { Capture } from '@/lib/db/types';

interface CaptureDetailsProps {
  captureId: string;
  onBack: () => void;
}

export default function CaptureDetails({ captureId, onBack }: CaptureDetailsProps) {
  const [capture, setCapture] = useState<Capture | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [originalImageUrl, setOriginalImageUrl] = useState<string>('');
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [showDetections, setShowDetections] = useState(true);

  // Memoized toggle function to prevent unnecessary re-renders
  const toggleDetections = useCallback(() => {
    setShowDetections(prev => !prev);
  }, []);

  // Load capture data
  useEffect(() => {
    let mounted = true;
    
    async function loadCapture() {
      try {
        setIsLoading(true);
        setError(null);
        const captureData = await getCaptureById(captureId);
        
        if (mounted) {
          if (captureData) {
            setCapture(captureData);
            
            // Create blob URLs for images
            if (captureData.originalImageBlob) {
              setOriginalImageUrl(URL.createObjectURL(captureData.originalImageBlob));
            }
            if (captureData.processedImageBlob) {
              setProcessedImageUrl(URL.createObjectURL(captureData.processedImageBlob));
            }
          } else {
            setError('Capture not found');
          }
        }
      } catch (err) {
        console.error('Error loading capture:', err);
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to load capture');
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    }

    loadCapture();
    
    return () => {
      mounted = false;
    };
  }, [captureId]); // Removed URL dependencies to prevent infinite loop

  // Cleanup blob URLs on unmount only
  useEffect(() => {
    return () => {
      if (originalImageUrl) URL.revokeObjectURL(originalImageUrl);
      if (processedImageUrl) URL.revokeObjectURL(processedImageUrl);
    };
  }, [originalImageUrl, processedImageUrl]);

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="mb-4 flex items-center justify-between">
          <button 
            onClick={onBack}
            className="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
          >
            <ArrowLeft size={16} className="mr-1" />
            Back to History
          </button>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300 ml-3">Loading capture...</p>
        </div>
      </div>
    );
  }

  if (error || !capture) {
    return (
      <div className="p-4">
        <div className="mb-4 flex items-center justify-between">
          <button 
            onClick={onBack}
            className="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
          >
            <ArrowLeft size={16} className="mr-1" />
            Back to History
          </button>
        </div>
        <div className="text-center py-12 text-red-500">
          <p>Error: {error || 'Capture not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <button 
          onClick={onBack}
          className="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to History
        </button>
        
        <div className="text-sm font-medium">
          Capture {capture.captureId.substring(0, 8)}
        </div>
      </div>
      
      <div className="space-y-4">
        {/* Capture Info */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded p-3 text-xs">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Captured:</span>
              <div>{new Date(capture.captureTimestamp).toLocaleString()}</div>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Objects:</span>
              <div>{capture.detectionResults.length}</div>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Sync Status:</span>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  capture.syncStatus === 'synced' ? 'bg-green-500' : 
                  capture.syncStatus === 'pending' ? 'bg-yellow-500' : 
                  'bg-red-500'
                }`}></div>
                <span className="capitalize">{capture.syncStatus}</span>
              </div>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Frame ID:</span>
              <div>{capture.frameId.substring(0, 8)}...</div>
            </div>
          </div>
        </div>

        {/* Toggle Detections */}
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">Images</div>
          <button
            onClick={toggleDetections}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            {showDetections ? <EyeOff size={12} /> : <Eye size={12} />}
            {showDetections ? 'Hide' : 'Show'} Detections
          </button>
        </div>

        {/* Original vs Processed Images */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <div className="space-y-1">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Original</div>
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded overflow-hidden relative">
              {originalImageUrl ? (
                <Image 
                  src={originalImageUrl} 
                  alt="Original capture"
                  fill
                  className="object-cover"
                  unoptimized
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                  No Original Image
                </div>
              )}
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">
              {showDetections ? 'With Detections' : 'Processed'}
            </div>
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded overflow-hidden relative">
              {(showDetections ? processedImageUrl : originalImageUrl) ? (
                <Image 
                  src={showDetections ? processedImageUrl! : originalImageUrl!} 
                  alt={showDetections ? "Processed with detections" : "Original"}
                  fill
                  className="object-cover"
                  unoptimized
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                  No Processed Image
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Detection Results */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Detection Results ({capture.detectionResults.length})</div>
          <div className="bg-gray-100 dark:bg-gray-900 rounded p-3">
            {capture.detectionResults.length === 0 ? (
              <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-4">
                No objects detected in this image
              </div>
            ) : (
              <div className="space-y-2">
                {capture.detectionResults.map((detection, i) => (
                  <div 
                    key={detection.id || i} 
                    className="text-xs bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex justify-between items-center">
                      <div className="font-medium">{detection.class}</div>
                      <div className="font-mono bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded">
                        {Math.round(detection.confidence * 100)}%
                      </div>
                    </div>
                    <div className="text-gray-500 dark:text-gray-400 mt-1">
                      x₁={Math.round(detection.bbox.x1)}, y₁={Math.round(detection.bbox.y1)}, 
                      x₂={Math.round(detection.bbox.x2)}, y₂={Math.round(detection.bbox.y2)}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400 mt-1">
                      Size: {Math.round(detection.bbox.x2 - detection.bbox.x1)} × {Math.round(detection.bbox.y2 - detection.bbox.y1)} px
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
      </div>
    </div>
  );
}