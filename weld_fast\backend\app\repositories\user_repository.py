from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.exc import Integrity<PERSON>rror
from typing import Optional, List
import time

from ..models.database import User, UserRole
from ..utils.password import get_password_hash


class UserRepository:
    """Repository for user database operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(
        self,
        username: str,
        email: str,
        full_name: str,
        password: str,
        role: UserRole = UserRole.INSPECTOR
    ) -> Optional[User]:
        """Create a new user"""
        try:
            hashed_password = get_password_hash(password)
            current_time = int(time.time())
            
            user = User(
                username=username,
                email=email,
                full_name=full_name,
                hashed_password=hashed_password,
                role=role,
                created_at=current_time
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            return user
            
        except IntegrityError:
            await self.db.rollback()
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        result = await self.db.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        result = await self.db.execute(
            select(User).where(User.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def update_user(
        self,
        user_id: str,
        username: Optional[str] = None,
        email: Optional[str] = None,
        full_name: Optional[str] = None,
        role: Optional[UserRole] = None,
        is_active: Optional[bool] = None
    ) -> Optional[User]:
        """Update user details"""
        try:
            update_data = {}
            if username is not None:
                update_data[User.username] = username
            if email is not None:
                update_data[User.email] = email
            if full_name is not None:
                update_data[User.full_name] = full_name
            if role is not None:
                update_data[User.role] = role
            if is_active is not None:
                update_data[User.is_active] = is_active
            
            if update_data:
                await self.db.execute(
                    update(User).where(User.user_id == user_id).values(**update_data)
                )
                await self.db.commit()
            
            return await self.get_user_by_id(user_id)
            
        except IntegrityError:
            await self.db.rollback()
            return None
    
    async def update_password(self, user_id: str, new_password: str) -> bool:
        """Update user password"""
        try:
            hashed_password = get_password_hash(new_password)
            await self.db.execute(
                update(User).where(User.user_id == user_id).values(
                    hashed_password=hashed_password
                )
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False
    
    async def update_last_login(self, user_id: str) -> None:
        """Update user's last login timestamp"""
        current_time = int(time.time())
        await self.db.execute(
            update(User).where(User.user_id == user_id).values(
                last_login=current_time
            )
        )
        await self.db.commit()
    
    async def deactivate_user(self, user_id: str) -> bool:
        """Deactivate a user"""
        try:
            await self.db.execute(
                update(User).where(User.user_id == user_id).values(
                    is_active=False
                )
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False
    
    async def delete_user(self, user_id: str) -> bool:
        """Delete a user (hard delete)"""
        try:
            await self.db.execute(
                delete(User).where(User.user_id == user_id)
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False
    
    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        role: Optional[UserRole] = None,
        active_only: bool = True
    ) -> List[User]:
        """List users with pagination and filtering"""
        query = select(User)
        
        if role:
            query = query.where(User.role == role)
        
        if active_only:
            query = query.where(User.is_active == True)
        
        query = query.offset(skip).limit(limit).order_by(User.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count_users(self, role: Optional[UserRole] = None, active_only: bool = True) -> int:
        """Count users with filtering"""
        from sqlalchemy import func
        
        query = select(func.count(User.user_id))
        
        if role:
            query = query.where(User.role == role)
        
        if active_only:
            query = query.where(User.is_active == True)
        
        result = await self.db.execute(query)
        return result.scalar()