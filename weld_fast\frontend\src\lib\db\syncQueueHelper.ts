// src/lib/db/syncQueueHelper.ts
// Centralized sync queue operations helper to eliminate code duplication

import { getDB } from './index';
import type { SyncQueueItem } from './db-service';

/**
 * Standard priority levels for sync operations
 */
export const SYNC_PRIORITIES = {
  CRITICAL: 10,    // Frame creation, critical operations
  HIGH: 7,         // Capture creation, important updates
  MEDIUM: 4,       // Regular updates, modifications
  LOW: 1,          // Background cleanup, non-urgent operations
} as const;

/**
 * Create a standardized sync queue item
 */
export function createSyncQueueItem(
  operationType: 'create' | 'update' | 'delete',
  objectType: 'frame' | 'capture',
  objectId: string,
  options: {
    priority?: number;
    frameId?: string;
    metadata?: Record<string, unknown>;
  } = {}
): Omit<SyncQueueItem, 'queueId'> {
  const now = Date.now();
  
  // Build context if frameId or metadata is provided
  const context: { frameId?: string; [key: string]: unknown } | undefined = 
    (options.frameId || options.metadata) ? {} : undefined;
    
  if (context) {
    if (options.frameId) {
      context.frameId = options.frameId;
    }
    if (options.metadata) {
      Object.assign(context, options.metadata);
    }
  }

  const item: Omit<SyncQueueItem, 'queueId'> = {
    operationType,
    objectType,
    objectId,
    priority: options.priority ?? SYNC_PRIORITIES.MEDIUM,
    createdAt: now,
    attemptCount: 0,
    status: 'pending',
    ...(context && { context })
  };

  return item;
}

/**
 * Add item to sync queue with standardized structure
 */
export async function addToSyncQueueStandardized(
  operationType: 'create' | 'update' | 'delete',
  objectType: 'frame' | 'capture',
  objectId: string,
  options: {
    priority?: number;
    frameId?: string;
    metadata?: Record<string, unknown>;
  } = {}
): Promise<number> {
  const db = await getDB();
  const item = createSyncQueueItem(operationType, objectType, objectId, options);
  return await db.add('syncQueue', item);
}

/**
 * Add frame operation to sync queue with high priority
 */
export async function addFrameToSyncQueue(
  operationType: 'create' | 'update' | 'delete',
  frameId: string,
  metadata?: Record<string, unknown>
): Promise<number> {
  return addToSyncQueueStandardized(operationType, 'frame', frameId, {
    priority: SYNC_PRIORITIES.CRITICAL,
    frameId,
    metadata
  });
}

/**
 * Add capture operation to sync queue with medium-high priority
 */
export async function addCaptureToSyncQueue(
  operationType: 'create' | 'update' | 'delete',
  captureId: string,
  frameId: string,
  metadata?: Record<string, unknown>
): Promise<number> {
  return addToSyncQueueStandardized(operationType, 'capture', captureId, {
    priority: SYNC_PRIORITIES.HIGH,
    frameId,
    metadata
  });
}

/**
 * Batch add multiple items to sync queue in a single transaction
 */
export async function addBatchToSyncQueue(
  items: Array<{
    operationType: 'create' | 'update' | 'delete';
    objectType: 'frame' | 'capture';
    objectId: string;
    priority?: number;
    frameId?: string;
    metadata?: Record<string, unknown>;
  }>
): Promise<number[]> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readwrite');
  const store = tx.objectStore('syncQueue');
  
  const queueIds: number[] = [];
  
  for (const item of items) {
    const queueItem = createSyncQueueItem(
      item.operationType,
      item.objectType,
      item.objectId,
      {
        priority: item.priority,
        frameId: item.frameId,
        metadata: item.metadata
      }
    );
    
    const queueId = await store.add(queueItem);
    queueIds.push(queueId);
  }
  
  await tx.done;
  return queueIds;
}

/**
 * Update sync queue item status with error handling
 */
export async function updateSyncQueueItemStatus(
  queueId: number,
  status: 'pending' | 'processing' | 'completed' | 'failed'
): Promise<void> {
  const db = await getDB();
  const item = await db.get('syncQueue', queueId);
  
  if (!item) {
    console.warn(`Sync queue item ${queueId} not found`);
    return;
  }
  
  const updatedItem: SyncQueueItem = {
    ...item,
    status,
    lastAttempt: Date.now(),
    attemptCount: status === 'processing' ? item.attemptCount + 1 : item.attemptCount
  };
  
  await db.put('syncQueue', updatedItem);
}

/**
 * Clean up old completed sync items (older than 24 hours)
 */
export async function cleanupOldSyncItems(): Promise<number> {
  const db = await getDB();
  const tx = db.transaction('syncQueue', 'readwrite');
  const store = tx.objectStore('syncQueue');
  
  const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
  const allItems = await store.getAll();
  
  let deletedCount = 0;
  
  for (const item of allItems) {
    if (item.status === 'completed' && item.lastAttempt && item.lastAttempt < cutoffTime) {
      if (item.queueId !== undefined) {
        await store.delete(item.queueId);
        deletedCount++;
      }
    }
  }
  
  await tx.done;
  return deletedCount;
}
