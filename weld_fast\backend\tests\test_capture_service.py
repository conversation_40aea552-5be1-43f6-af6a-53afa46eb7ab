import pytest
import time
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.services.capture_service import CaptureService
from app.models.database import Base, Capture, Frame, SyncStatus, FrameStatus
from app.schemas.frame import CaptureCreate, CaptureUpdate, DetectionResult


# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_captures.db"
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="function")
def test_db():
    """Create test database and session"""
    Base.metadata.create_all(bind=test_engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def sample_frame(test_db: Session):
    """Create a sample frame for testing"""
    frame = Frame(
        frame_id="test-frame-123",
        model_number="MODEL-001",
        machine_serial_number="MACHINE-001", 
        inspector_name="Test Inspector",
        creation_timestamp=int(time.time()),
        last_modified_timestamp=int(time.time()),
        status=FrameStatus.ACTIVE,
        capture_count=0
    )
    test_db.add(frame)
    test_db.commit()
    return frame


@pytest.fixture
def sample_detection_results():
    """Sample detection results for testing"""
    return [
        DetectionResult(
            id="det-1",
            class_name="person",
            confidence=0.95,
            bbox={"x1": 100, "y1": 100, "x2": 200, "y2": 200}
        ),
        DetectionResult(
            id="det-2", 
            class_name="car",
            confidence=0.87,
            bbox={"x1": 300, "y1": 150, "x2": 400, "y2": 250}
        )
    ]


@pytest.fixture
def sample_image_data():
    """Sample image data for testing"""
    # Create a small test image (fake binary data)
    return b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'


class TestCaptureService:
    """Test cases for CaptureService"""
    
    def test_create_capture_success(self, test_db: Session, sample_frame: Frame, sample_detection_results, sample_image_data):
        """Test successful capture creation"""
        service = CaptureService(test_db)
        
        capture_data = CaptureCreate(
            frame_id=sample_frame.frame_id,
            detection_results=sample_detection_results,
            original_image_data=sample_image_data,
            processed_image_data=sample_image_data
        )
        
        # Mock thumbnail generation to avoid PIL dependency in tests
        with patch.object(service, '_generate_thumbnail', return_value=b'thumbnail_data'):
            capture = service.create_capture(capture_data)
        
        assert capture.capture_id is not None
        assert capture.frame_id == sample_frame.frame_id
        assert len(capture.detection_results) == 2
        assert capture.sync_status == SyncStatus.SYNCED
        assert capture.sync_version == 1
        assert capture.original_image_blob == sample_image_data
        assert capture.processed_image_blob == sample_image_data
        assert capture.thumbnail_blob == b'thumbnail_data'
        
        # Check frame capture count was updated
        test_db.refresh(sample_frame)
        assert sample_frame.capture_count == 1
    
    def test_create_capture_invalid_frame(self, test_db: Session, sample_detection_results):
        """Test capture creation with invalid frame ID"""
        service = CaptureService(test_db)
        
        capture_data = CaptureCreate(
            frame_id="non-existent-frame",
            detection_results=sample_detection_results
        )
        
        with pytest.raises(ValueError, match="Frame with ID non-existent-frame not found"):
            service.create_capture(capture_data)
    
    def test_get_capture_success(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test successful capture retrieval"""
        service = CaptureService(test_db)
        
        # Create a capture first
        capture_data = CaptureCreate(
            frame_id=sample_frame.frame_id,
            detection_results=sample_detection_results
        )
        created_capture = service.create_capture(capture_data)
        
        # Retrieve the capture
        retrieved_capture = service.get_capture(created_capture.capture_id)
        
        assert retrieved_capture is not None
        assert retrieved_capture.capture_id == created_capture.capture_id
        assert retrieved_capture.frame_id == sample_frame.frame_id
    
    def test_get_capture_not_found(self, test_db: Session):
        """Test capture retrieval with non-existent ID"""
        service = CaptureService(test_db)
        
        capture = service.get_capture("non-existent-id")
        assert capture is None
    
    def test_update_capture_success(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test successful capture update"""
        service = CaptureService(test_db)
        
        # Create a capture first
        capture_data = CaptureCreate(
            frame_id=sample_frame.frame_id,
            detection_results=sample_detection_results
        )
        created_capture = service.create_capture(capture_data)
        
        # Update the capture
        new_detection = DetectionResult(
            id="det-3",
            class_name="bike", 
            confidence=0.75,
            bbox={"x1": 500, "y1": 300, "x2": 600, "y2": 400}
        )
        
        update_data = CaptureUpdate(
            detection_results=[new_detection]
        )
        
        updated_capture = service.update_capture(created_capture.capture_id, update_data, expected_version=1)
        
        assert updated_capture.sync_version == 2
        assert len(updated_capture.detection_results) == 1
        assert updated_capture.detection_results[0]['class'] == 'bike'
    
    def test_update_capture_version_conflict(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test capture update with version conflict"""
        service = CaptureService(test_db)
        
        # Create a capture first
        capture_data = CaptureCreate(
            frame_id=sample_frame.frame_id,
            detection_results=sample_detection_results
        )
        created_capture = service.create_capture(capture_data)
        
        update_data = CaptureUpdate(detection_results=[])
        
        # Try to update with wrong version
        with pytest.raises(ValueError, match="Capture version conflict"):
            service.update_capture(created_capture.capture_id, update_data, expected_version=999)
    
    def test_update_capture_not_found(self, test_db: Session):
        """Test capture update with non-existent ID"""
        service = CaptureService(test_db)
        
        update_data = CaptureUpdate(detection_results=[])
        
        with pytest.raises(ValueError, match="Capture with ID non-existent not found"):
            service.update_capture("non-existent", update_data)
    
    def test_delete_capture_success(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test successful capture deletion"""
        service = CaptureService(test_db)
        
        # Create a capture first
        capture_data = CaptureCreate(
            frame_id=sample_frame.frame_id,
            detection_results=sample_detection_results
        )
        created_capture = service.create_capture(capture_data)
        
        # Verify frame capture count
        test_db.refresh(sample_frame)
        assert sample_frame.capture_count == 1
        
        # Delete the capture
        deleted = service.delete_capture(created_capture.capture_id)
        assert deleted is True
        
        # Verify capture is gone
        capture = service.get_capture(created_capture.capture_id)
        assert capture is None
        
        # Verify frame capture count was decremented
        test_db.refresh(sample_frame)
        assert sample_frame.capture_count == 0
    
    def test_delete_capture_not_found(self, test_db: Session):
        """Test deletion of non-existent capture"""
        service = CaptureService(test_db)
        
        deleted = service.delete_capture("non-existent")
        assert deleted is False
    
    def test_get_captures_by_frame_success(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test getting captures by frame with pagination"""
        service = CaptureService(test_db)
        
        # Create multiple captures
        for i in range(5):
            capture_data = CaptureCreate(
                frame_id=sample_frame.frame_id,
                detection_results=sample_detection_results
            )
            service.create_capture(capture_data)
            time.sleep(0.01)  # Small delay to ensure different timestamps
        
        # Test pagination
        captures, total = service.get_captures_by_frame(sample_frame.frame_id, page=1, limit=3)
        
        assert total == 5
        assert len(captures) == 3
        
        # Test sorting (default is desc by timestamp)
        timestamps = [c.capture_timestamp for c in captures]
        assert timestamps == sorted(timestamps, reverse=True)
        
        # Test second page
        captures_page2, total_page2 = service.get_captures_by_frame(sample_frame.frame_id, page=2, limit=3)
        assert total_page2 == 5
        assert len(captures_page2) == 2
    
    def test_get_captures_by_frame_invalid_frame(self, test_db: Session):
        """Test getting captures for non-existent frame"""
        service = CaptureService(test_db)
        
        with pytest.raises(ValueError, match="Frame with ID non-existent not found"):
            service.get_captures_by_frame("non-existent")
    
    def test_get_capture_count_by_frame(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test getting capture count for a frame"""
        service = CaptureService(test_db)
        
        # Initially should be 0
        count = service.get_capture_count_by_frame(sample_frame.frame_id)
        assert count == 0
        
        # Create some captures
        for i in range(3):
            capture_data = CaptureCreate(
                frame_id=sample_frame.frame_id,
                detection_results=sample_detection_results
            )
            service.create_capture(capture_data)
        
        # Should now be 3
        count = service.get_capture_count_by_frame(sample_frame.frame_id)
        assert count == 3
    
    def test_update_frame_capture_count(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test updating frame capture count"""
        service = CaptureService(test_db)
        
        # Create captures
        for i in range(2):
            capture_data = CaptureCreate(
                frame_id=sample_frame.frame_id,
                detection_results=sample_detection_results
            )
            service.create_capture(capture_data)
        
        # Manually mess up the count
        sample_frame.capture_count = 999
        test_db.commit()
        
        # Fix it with the service method
        service.update_frame_capture_count(sample_frame.frame_id)
        
        # Verify it's correct now
        test_db.refresh(sample_frame)
        assert sample_frame.capture_count == 2
    
    def test_get_captures_by_sync_status(self, test_db: Session, sample_frame: Frame, sample_detection_results):
        """Test getting captures by sync status"""
        service = CaptureService(test_db)
        
        # Create captures
        for i in range(3):
            capture_data = CaptureCreate(
                frame_id=sample_frame.frame_id,
                detection_results=sample_detection_results
            )
            service.create_capture(capture_data)
        
        # All should be synced initially
        synced_captures = service.get_captures_by_sync_status(SyncStatus.SYNCED)
        assert len(synced_captures) == 3
        
        # Change one to pending
        capture = synced_captures[0]
        capture.sync_status = SyncStatus.PENDING
        test_db.commit()
        
        # Test filtering
        pending_captures = service.get_captures_by_sync_status(SyncStatus.PENDING)
        assert len(pending_captures) == 1
        
        synced_captures = service.get_captures_by_sync_status(SyncStatus.SYNCED)
        assert len(synced_captures) == 2
    
    @patch('app.services.capture_service.Image')
    def test_generate_thumbnail_success(self, mock_image, test_db: Session):
        """Test thumbnail generation"""
        service = CaptureService(test_db)
        
        # Mock PIL Image operations
        mock_img_instance = Mock()
        mock_image.open.return_value = mock_img_instance
        mock_img_instance.mode = 'RGB'
        mock_img_instance.save.return_value = None
        
        # Mock BytesIO
        with patch('app.services.capture_service.io.BytesIO') as mock_bytesio:
            mock_bytesio_instance = Mock()
            mock_bytesio.return_value = mock_bytesio_instance
            mock_bytesio_instance.getvalue.return_value = b'thumbnail_data'
            
            result = service._generate_thumbnail(b'image_data')
            
            assert result == b'thumbnail_data'
            mock_image.open.assert_called_once()
            mock_img_instance.thumbnail.assert_called_once()
            mock_img_instance.save.assert_called_once()
    
    @patch('app.services.capture_service.Image')
    def test_generate_thumbnail_failure(self, mock_image, test_db: Session):
        """Test thumbnail generation failure"""
        service = CaptureService(test_db)
        
        # Mock PIL to raise an exception
        mock_image.open.side_effect = Exception("PIL error")
        
        result = service._generate_thumbnail(b'invalid_image_data')
        assert result is None


if __name__ == "__main__":
    pytest.main([__file__])