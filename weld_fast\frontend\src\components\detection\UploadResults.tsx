'use client';

import { useState, useEffect } from 'react';
import { Eye, CheckCircle, XCircle, Search, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProcessingResult } from '@/lib/upload/batchProcessor';
import { UploadFile } from './FileDropZone';
import { getCaptureById } from '@/lib/db/captureOperations';
import { createManagedBlobUrl, revokeManagedBlobUrl } from '@/lib/upload/blobUtils';
import Image from 'next/image';

interface UploadResultsProps {
  results: ProcessingResult[];
  files: UploadFile[];
  onViewCapture?: (captureId: string) => void;
}

export default function UploadResults({ results, files, onViewCapture }: UploadResultsProps) {
  const [processedImageUrls, setProcessedImageUrls] = useState<Map<string, string>>(new Map());
  const [searchTerm, setSearchTerm] = useState('');

  // Load capture data for successful results to get processed images
  useEffect(() => {
    const loadCaptures = async () => {
      const urlMap = new Map<string, string>();
      
      for (const result of results) {
        if (result.success && result.captureId) {
          try {
            const capture = await getCaptureById(result.captureId);
            if (capture && capture.processedImageBlob) {
              // Create managed blob URL for processed image
              const url = createManagedBlobUrl(capture.processedImageBlob, `processed-${result.captureId}`);
              urlMap.set(result.captureId, url);
            }
          } catch (error) {
            console.error('Failed to load capture:', error);
          }
        }
      }
      
      setProcessedImageUrls(urlMap);
    };

    if (results.length > 0) {
      loadCaptures();
    }

    // Cleanup function to revoke blob URLs
    return () => {
      processedImageUrls.forEach((url, captureId) => {
        revokeManagedBlobUrl(`processed-${captureId}`);
      });
    };
  }, [results, processedImageUrls]);

  // Get file for result
  const getFileForResult = (result: ProcessingResult) => {
    return files.find(f => f.id === result.fileId);
  };

  // Filter results based on search
  const filteredResults = results.filter(result => {
    if (!searchTerm) return true;
    const file = getFileForResult(result);
    const fileName = file?.file.name.toLowerCase() || '';
    return fileName.includes(searchTerm.toLowerCase());
  });

  const completedCount = results.filter(r => r.success).length;
  const failedCount = results.filter(r => !r.success).length;
  const totalDetections = results.reduce((sum, r) => sum + (r.detectionCount || 0), 0);

  if (results.length === 0) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <BarChart3 className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Processing Results
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {results.length} {results.length === 1 ? 'image' : 'images'} processed
              </p>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm w-64"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {completedCount}
            </div>
            <div className="text-sm text-green-700 dark:text-green-300">Successful</div>
          </div>
          <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {failedCount}
            </div>
            <div className="text-sm text-red-700 dark:text-red-300">Failed</div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {totalDetections}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">Total Detections</div>
          </div>
        </div>
      </div>

      {/* Results Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredResults.map((result) => {
            const file = getFileForResult(result);
            const hasProcessedImage = result.success && result.captureId && processedImageUrls.has(result.captureId);

            return (
              <div
                key={result.fileId}
                className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow"
              >
                {/* Image Display */}
                <div className="relative">
                  {hasProcessedImage ? (
                    /* Show side-by-side comparison */
                    <div className="grid grid-cols-2 h-48">
                      {/* Original */}
                      {file && (
                        <div className="relative">
                          <Image
                            src={file.preview}
                            alt={`${file.file.name} (original)`}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                          />
                          <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                            Original
                          </div>
                        </div>
                      )}
                      
                      {/* Processed */}
                      <div className="relative">
                        <Image
                          src={processedImageUrls.get(result.captureId!)!}
                          alt={`${file?.file.name} (processed)`}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                        />
                        <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                          Processed
                        </div>
                        <div className="absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded font-bold">
                          {result.detectionCount || 0}
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Show original only */
                    <div className="relative h-48">
                      {file ? (
                        <Image
                          src={file.preview}
                          alt={file.file.name}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                          <span className="text-gray-400">No preview</span>
                        </div>
                      )}
                      
                      {/* Status overlay */}
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                        {result.success ? (
                          <div className="text-center text-white">
                            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                            <span className="text-sm">Processing Complete</span>
                          </div>
                        ) : (
                          <div className="text-center text-white">
                            <XCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
                            <span className="text-sm">Processing Failed</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Card Content */}
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">
                      {file?.file.name || 'Unknown file'}
                    </h4>
                    {result.success && result.captureId && onViewCapture && (
                      <button
                        onClick={() => onViewCapture(result.captureId!)}
                        className="p-1.5 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <div className="space-y-2">
                    {/* File info */}
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                      <span>Size:</span>
                      <span>{file ? (file.file.size / 1024 / 1024).toFixed(1) : '0'} MB</span>
                    </div>

                    {result.success && (
                      <>
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span>Detections:</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            {result.detectionCount || 0}
                          </span>
                        </div>
                        {result.processingTime && (
                          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                            <span>Processing time:</span>
                            <span>{(result.processingTime / 1000).toFixed(1)}s</span>
                          </div>
                        )}
                      </>
                    )}

                    {/* Error message */}
                    {!result.success && result.error && (
                      <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                        {result.error}
                      </div>
                    )}

                    {/* Status badge */}
                    <div className="flex justify-center pt-2">
                      <span className={cn(
                        "px-3 py-1 rounded-full text-xs font-medium",
                        result.success 
                          ? "bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300"
                          : "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300"
                      )}>
                        {result.success ? '✓ Success' : '✗ Failed'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredResults.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No results match your search.</p>
          </div>
        )}
      </div>
    </div>
  );
}