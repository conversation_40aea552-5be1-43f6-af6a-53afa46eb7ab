'use client';

import { reactiveSyncService } from './reactiveSyncService';
import { enhancedSyncStateManager } from './enhancedSyncStateManager';
import { getSyncStats, getFrameSyncStats } from '@/lib/db/syncQueueOperations';
import type { SyncProgress } from './types';

/**
 * Reactive Sync Functions
 * 
 * Clean function-based API that replaces all legacy syncManager exports.
 * Provides the same interface but with full reactive integration.
 */

/**
 * Start reactive sync for all pending items
 */
export async function startReactiveSync(): Promise<SyncProgress> {
  console.log('[reactiveSyncFunctions] Starting reactive sync');
  
  try {
    const result = await reactiveSyncService.processSyncQueue();
    
    // Update enhanced sync state manager with completion
    const stats = await getSyncStats();
    const completionProgress: SyncProgress = {
      ...result,
      isProcessing: false,
      currentItem: undefined
    };
    
    enhancedSyncStateManager.updateSyncState(stats, completionProgress);
    
    console.log('[reactiveSyncFunctions] Reactive sync completed:', result);
    return result;
  } catch (error) {
    console.error('[reactiveSyncFunctions] Reactive sync failed:', error);
    
    // Update state manager with error completion
    const stats = await getSyncStats();
    enhancedSyncStateManager.updateSyncState(stats, {
      totalItems: 0,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      isProcessing: false,
      currentItem: undefined
    });
    
    throw error;
  }
}

/**
 * Start reactive sync for specific frame only
 */
export async function startReactiveFrameSync(frameId: string): Promise<SyncProgress> {
  console.log('[reactiveSyncFunctions] Starting reactive frame sync for:', frameId);
  
  try {
    const result = await reactiveSyncService.processSyncQueueForFrame(frameId);
    
    // Update enhanced sync state manager with completion
    const stats = await getSyncStats();
    const completionProgress: SyncProgress = {
      ...result,
      isProcessing: false,
      currentItem: undefined
    };
    
    enhancedSyncStateManager.updateSyncState(stats, completionProgress);
    
    console.log('[reactiveSyncFunctions] Reactive frame sync completed:', result);
    return result;
  } catch (error) {
    console.error('[reactiveSyncFunctions] Reactive frame sync failed:', error);
    
    // Update state manager with error completion
    const stats = await getSyncStats();
    enhancedSyncStateManager.updateSyncState(stats, {
      totalItems: 0,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      isProcessing: false,
      currentItem: undefined
    });
    
    throw error;
  }
}

/**
 * Stop reactive sync operation
 */
export function stopReactiveSync(): void {
  console.log('[reactiveSyncFunctions] Stopping reactive sync');
  reactiveSyncService.stopSync();
}

/**
 * Check if sync is currently processing
 */
export function isReactiveSyncing(): boolean {
  return reactiveSyncService.isSyncing;
}

/**
 * Get current sync status with reactive updates
 */
export async function getReactiveSyncStatus() {
  const stats = await getSyncStats();
  // Update sync state manager with current stats
  enhancedSyncStateManager.updateSyncState(stats);
  return stats;
}

/**
 * Get frame-specific sync status with reactive updates
 */
export async function getReactiveFrameSyncStatus(frameId: string) {
  const frameStats = await getFrameSyncStats(frameId);
  
  // Update frame state in enhanced sync state manager
  if (frameStats) {
    enhancedSyncStateManager.updateItemSyncStatus(
      frameId,
      frameId,
      'frame',
      frameStats.pending > 0 ? 'pending' : 'synced'
    );
  }
  
  return frameStats;
}

/**
 * Retry failed sync items
 */
export async function retryReactiveFailedItems(): Promise<SyncProgress> {
  console.log('[reactiveSyncFunctions] Retrying failed items');
  
  try {
    const result = await reactiveSyncService.retryFailedItems();
    
    // Update enhanced sync state manager with completion
    const stats = await getSyncStats();
    const completionProgress: SyncProgress = {
      ...result,
      isProcessing: false,
      currentItem: undefined
    };
    
    enhancedSyncStateManager.updateSyncState(stats, completionProgress);
    
    console.log('[reactiveSyncFunctions] Retry completed:', result);
    return result;
  } catch (error) {
    console.error('[reactiveSyncFunctions] Retry failed:', error);
    
    // Update state manager with error completion
    const stats = await getSyncStats();
    enhancedSyncStateManager.updateSyncState(stats, {
      totalItems: 0,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      isProcessing: false,
      currentItem: undefined
    });
    
    throw error;
  }
}

/**
 * Get sync service stats
 */
export async function getReactiveSyncStats() {
  return await reactiveSyncService.getSyncStats();
}

// Legacy compatibility exports (these mirror the old syncManager API)
export {
  startReactiveSync as startSync,
  startReactiveFrameSync as startFrameSync,
  stopReactiveSync as stopSync,
  isReactiveSyncing as isSyncing,
  getReactiveSyncStatus as getSyncStatus,
  getReactiveFrameSyncStatus as getFrameSyncStatus,
  retryReactiveFailedItems as retryFailedItems
};

// Re-export types for convenience
export type { SyncProgress } from './types';