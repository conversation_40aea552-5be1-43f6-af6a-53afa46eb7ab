"""
PDF Report Generation Service for Weld Detection Image Comparisons
"""

import io
import asyncio
from typing import List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from PIL import Image as PILImage

from ..models.database import Frame, Capture


class PDFReportService:
    """Service for generating PDF reports with image comparisons."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def generate_frame_comparison_report(self, frame_id: str) -> bytes:
        """Generate a PDF report comparing original and processed images for a frame."""
        
        # Get frame details
        frame_result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        frame = frame_result.scalar_one_or_none()
        
        if not frame:
            raise ValueError(f"Frame {frame_id} not found")

        # Get all captures for this frame
        captures_result = await self.db.execute(
            select(Capture).where(Capture.frame_id == frame_id).order_by(Capture.capture_timestamp)
        )
        captures = captures_result.scalars().all()

        # Create PDF buffer
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch, bottomMargin=0.5*inch)
        
        # Build PDF content
        story = []
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER
        )
        
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            alignment=TA_LEFT
        )

        # Title
        story.append(Paragraph("Weld Detection Inspection Report", title_style))
        story.append(Spacer(1, 20))

        # Frame information
        frame_info = [
            ["Frame ID:", frame.frame_id],
            ["Machine Serial:", frame.machine_serial_number],
            ["Model Number:", frame.model_number],
            ["Inspector:", frame.inspector_name],
            ["Created:", datetime.fromtimestamp(frame.creation_timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")],
            ["Total Captures:", str(len(captures))]
        ]
        
        frame_table = Table(frame_info, colWidths=[2*inch, 4*inch])
        frame_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(frame_table)
        story.append(Spacer(1, 30))

        # Process each capture
        for i, capture in enumerate(captures, 1):
            if i > 1:  # Add page break between captures
                story.append(PageBreak())
            
            story.append(Paragraph(f"Capture {i} - Image Comparison", header_style))
            story.append(Spacer(1, 10))

            # Capture details
            total_detections = len(capture.detection_results) if capture.detection_results else 0
            detection_classes = []
            if capture.detection_results:
                detection_classes = list(set([det.get('class', 'Unknown') for det in capture.detection_results]))

            capture_info = [
                ["Capture ID:", capture.capture_id],
                ["Timestamp:", datetime.fromtimestamp(capture.capture_timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")],
                ["Total Detections:", str(total_detections)],
                ["Detection Classes:", ", ".join(detection_classes) if detection_classes else "None"],
                ["Sync Status:", capture.sync_status.capitalize()]
            ]

            capture_table = Table(capture_info, colWidths=[2*inch, 4*inch])
            capture_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(capture_table)
            story.append(Spacer(1, 20))

            # Image comparison
            try:
                # Create images side by side
                if capture.original_image_blob and capture.processed_image_blob:
                    # Create temporary images
                    original_img = self._create_image_from_blob(capture.original_image_blob, "Original Image")
                    processed_img = self._create_image_from_blob(capture.processed_image_blob, "With Detections")
                    
                    if original_img and processed_img:
                        # Create comparison table
                        comparison_data = [
                            ["Original Image", "With Detection Overlays"],
                            [original_img, processed_img]
                        ]
                        
                        comparison_table = Table(comparison_data, colWidths=[3*inch, 3*inch])
                        comparison_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, 0), 12),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black),
                            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                        ]))
                        
                        story.append(comparison_table)
                    else:
                        story.append(Paragraph("Images could not be loaded", styles['Normal']))
                else:
                    story.append(Paragraph("Images not available for this capture", styles['Normal']))
            except Exception as e:
                story.append(Paragraph(f"Error loading images: {str(e)}", styles['Normal']))

            story.append(Spacer(1, 20))

            # Detection details if any
            if capture.detection_results and len(capture.detection_results) > 0:
                story.append(Paragraph("Detection Details:", styles['Heading3']))
                
                detection_data = [["Class", "Confidence", "Position (x, y, w, h)"]]
                for det in capture.detection_results:
                    bbox = det.get('bbox', {})
                    position = f"({bbox.get('x', 0):.1f}, {bbox.get('y', 0):.1f}, {bbox.get('width', 0):.1f}, {bbox.get('height', 0):.1f})"
                    detection_data.append([
                        det.get('class', 'Unknown'),
                        f"{det.get('confidence', 0):.2f}",
                        position
                    ])
                
                detection_table = Table(detection_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
                detection_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ]))
                
                story.append(detection_table)

        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def _create_image_from_blob(self, image_blob: bytes, title: str = "") -> Optional[Image]:
        """Create a ReportLab Image object from blob data."""
        try:
            # Create PIL Image from blob
            pil_image = PILImage.open(io.BytesIO(image_blob))
            
            # Resize if too large (max 250x250 pixels for PDF)
            max_size = (250, 250)
            pil_image.thumbnail(max_size, PILImage.Resampling.LANCZOS)
            
            # Save to BytesIO
            img_buffer = io.BytesIO()
            pil_image.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # Create ReportLab Image
            img = Image(img_buffer, width=2.5*inch, height=2.5*inch)
            return img
            
        except Exception as e:
            print(f"Error creating image from blob: {e}")
            return None

    async def get_frame_summary(self, frame_id: str) -> dict:
        """Get summary information for a frame."""
        frame_result = await self.db.execute(
            select(Frame).where(Frame.frame_id == frame_id)
        )
        frame = frame_result.scalar_one_or_none()
        
        if not frame:
            return {}

        captures_result = await self.db.execute(
            select(Capture).where(Capture.frame_id == frame_id)
        )
        captures = captures_result.scalars().all()

        total_detections = sum(len(c.detection_results or []) for c in captures)
        detection_classes = set()
        for capture in captures:
            if capture.detection_results:
                for det in capture.detection_results:
                    detection_classes.add(det.get('class', 'Unknown'))

        return {
            "frame_id": frame.frame_id,
            "machine_serial": frame.machine_serial_number,
            "model_number": frame.model_number,
            "inspector_name": frame.inspector_name,
            "created_at": datetime.fromtimestamp(frame.creation_timestamp / 1000),
            "total_captures": len(captures),
            "total_detections": total_detections,
            "detection_classes": list(detection_classes)
        }