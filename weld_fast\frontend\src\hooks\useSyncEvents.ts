'use client';

import { useEffect, useCallback } from 'react';
import { enhancedSyncStateManager } from '@/lib/sync/enhancedSyncStateManager';
import type { EnhancedSyncState } from '@/lib/sync/types';

/**
 * Custom hook to listen for sync completion events
 * Uses the enhanced reactive sync state system for real-time updates
 * @param callback Function to call when sync completes
 */
export function useSyncEvents(callback: () => void) {
  const stableCallback = useCallback(callback, [callback]);

  useEffect(() => {
    // Use enhanced sync state manager for more accurate sync completion detection
    let lastProcessingState = false;
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((syncState) => {
      // Trigger callback when sync processing completes (true -> false)
      if (lastProcessingState && !syncState.isProcessing) {
        console.log('[useSyncEvents] Sync completion detected, triggering callback');
        stableCallback();
      }
      lastProcessingState = syncState.isProcessing;
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to reactive sync state updates
 * @param callback Function to call when sync state changes
 */
export function useSyncState(callback: (state: EnhancedSyncState) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = enhancedSyncStateManager.getSyncState().subscribe(stableCallback);
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync stats only
 * @param callback Function to call when sync stats change
 */
export function useSyncStats(callback: (stats: { pending: number; processing: number; completed: number; failed: number; }) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((state) => {
      stableCallback(state.stats);
    });
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}

/**
 * Custom hook to subscribe to sync progress updates
 * @param callback Function to call when sync progress changes
 */
export function useSyncProgress(callback: (progress: { totalItems: number; processedItems: number; successfulItems: number; failedItems: number; currentItem?: string; isProcessing: boolean; } | null) => void) {
  const stableCallback = useCallback(callback, [callback]);
  
  useEffect(() => {
    const subscription = enhancedSyncStateManager.getSyncState().subscribe((state) => {
      stableCallback(state.progress);
    });
    
    return () => subscription.unsubscribe();
  }, [stableCallback]);
}