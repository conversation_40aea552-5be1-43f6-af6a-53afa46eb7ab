// src/lib/db/frameOperations.ts
// Using crypto.randomUUID() instead of uuid package
import { Frame } from './types';
import { getDB } from './index';
import { addFrameToSyncQueue } from './syncQueueHelper';

// Create a new frame
export async function createFrame(frameData: Omit<Frame, 'frameId' | 'creationTimestamp' | 'lastModifiedTimestamp' | 'syncStatus'>): Promise<Frame> {
  const db = await getDB();
  
  const now = Date.now();
  const frameId = crypto.randomUUID();
  
  const frame: Frame = {
    ...frameData,
    frameId,
    creationTimestamp: now,
    lastModifiedTimestamp: now,
    syncStatus: 'pending',
    captureCount: 0,
    status: 'active'
  };
  
  await db.add('frames', frame);

  // Add to sync queue using standardized helper
  await addFrameToSyncQueue('create', frameId);
  
  return frame;
}

// Get a frame by ID
export async function getFrameById(frameId: string): Promise<Frame | undefined> {
  const db = await getDB();
  return db.get('frames', frameId);
}


// Update a frame
export async function updateFrame(frameId: string, updates: Partial<Omit<Frame, 'frameId' | 'creationTimestamp'>>): Promise<Frame> {
  const db = await getDB();
  
  // Get the current frame
  const frame = await db.get('frames', frameId);
  if (!frame) {
    throw new Error(`Frame with ID ${frameId} not found`);
  }
  
  // Update the frame
  const updatedFrame: Frame = {
    ...frame,
    ...updates,
    lastModifiedTimestamp: Date.now(),
  };
  
  // Handle different types of updates
  if (updates.syncStatus === 'synced') {
    // This is a sync completion update - keep the synced status and don't re-queue
    updatedFrame.syncStatus = 'synced';
  } else if (updates.syncStatus === 'pending' && 'lastSyncedAt' in updates) {
    // This is an internal sync status update - don't re-queue
    updatedFrame.syncStatus = 'pending';
  } else {
    // This is a user/system update (metadata, captures, etc.) - mark as pending and queue
    updatedFrame.syncStatus = 'pending';
    
    // Check if frame is already in sync queue to avoid duplicates
    const existingQueueItem = await db.getAllFromIndex('syncQueue', 'by-object', [frameId, 'frame']);
    const hasPendingSync = existingQueueItem.some(item => item.status === 'pending');
    
    if (!hasPendingSync) {
      // Add to sync queue only if not already queued with frame context
      await db.add('syncQueue', {
        operationType: 'update',
        objectType: 'frame',
        objectId: frameId,
        priority: 5, // Medium priority for frame updates
        createdAt: Date.now(),
        attemptCount: 0,
        status: 'pending',
        context: { frameId }
      });
    }
  }
  
  await db.put('frames', updatedFrame);
  
  return updatedFrame;
}

// Delete a frame and its captures (local storage only, keeps server data)
export async function deleteFrame(frameId: string): Promise<void> {
  const db = await getDB();
  const tx = db.transaction(['frames', 'captures'], 'readwrite');
  
  // Delete the frame
  await tx.objectStore('frames').delete(frameId);
  
  // Delete all captures for this frame
  const captureIndex = tx.objectStore('captures').index('by-frame');
  let cursor = await captureIndex.openCursor(frameId);
  
  while (cursor) {
    await cursor.delete();
    cursor = await cursor.continue();
  }
  
  // Commit the transaction
  await tx.done;
}

// Get all frames (with optional limit)
export async function getAllFrames(limit?: number): Promise<Frame[]> {
  const db = await getDB();
  const frames = await db.getAll('frames');
  
  // Sort by creation timestamp (newest first)
  frames.sort((a, b) => b.creationTimestamp - a.creationTimestamp);
  
  // Apply limit if specified
  if (limit && limit > 0) {
    return frames.slice(0, limit);
  }
  
  return frames;
}

// Increment the capture count for a frame
export async function incrementCaptureCount(frameId: string): Promise<void> {
  const db = await getDB();
  const tx = db.transaction(['frames', 'syncQueue'], 'readwrite');
  const frame = await tx.objectStore('frames').get(frameId);
  
  if (!frame) {
    throw new Error(`Frame with ID ${frameId} not found`);
  }
  
  frame.captureCount += 1;
  frame.lastModifiedTimestamp = Date.now();
  frame.syncStatus = 'pending';
  
  await tx.objectStore('frames').put(frame);
  
  // Check if frame is already in sync queue to avoid duplicates
  const syncQueueIndex = tx.objectStore('syncQueue').index('by-object');
  const existingQueueItems = await syncQueueIndex.getAll([frameId, 'frame']);
  const hasPendingSync = existingQueueItems.some(item => item.status === 'pending');
  
  if (!hasPendingSync) {
    // Add to sync queue only if not already queued with frame context
    await tx.objectStore('syncQueue').add({
      operationType: 'update',
      objectType: 'frame',
      objectId: frameId,
      priority: 5, // Medium priority for metadata updates
      createdAt: Date.now(),
      attemptCount: 0,
      status: 'pending',
      context: { frameId }
    });
  }
  
  await tx.done;
}