// src/app/detection/page.tsx
'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Header } from "@/components/layout/header";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import CameraPanel from '@/components/detection/CameraPanel';
import HistoryPanel from '@/components/detection/HistoryPanel';
import SessionEditor from '@/components/detection/SessionEditor';
import UploadPanel from '@/components/detection/UploadPanel';
import Link from 'next/link';
import { useSession } from '@/context/SessionContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { CaptureUpdatesProvider } from '@/context/CaptureUpdatesContext';

export default function DetectionPage() {
  return (
    <ProtectedRoute>
      <DetectionContent />
    </ProtectedRoute>
  );
}

function DetectionContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { frameId, modelNumber, machineSerialNumber, inspectorName, loadFrame, isLoading } = useSession();
  const [showSessionEditor, setShowSessionEditor] = useState(false);

  useEffect(() => {
    // Check if frameId is passed in URL (new session management)
    const urlFrameId = searchParams.get('frameId');
    
    if (urlFrameId) {
      loadFrame(urlFrameId);
    } else {
      // Legacy URL parameters support (fallback)
      const legacyModel = searchParams.get('model');
      const legacyMachine = searchParams.get('machine');
      const legacyInspector = searchParams.get('inspector');
      
      if (legacyModel && legacyMachine && legacyInspector) {
        // Redirect to home page to create proper session
        router.push('/');
        return;
      }
      
      // No session data - redirect to home
      if (!frameId) {
        router.push('/');
        return;
      }
    }
  }, [searchParams, frameId, loadFrame, router]);

  // Show loading state while session is being loaded
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading session...</p>
        </div>
      </div>
    );
  }

  // Show error state if no valid session
  if (!frameId || !modelNumber) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.996-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Active Session</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">Please create a new session to start detection.</p>
          <Link 
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create New Session
          </Link>
        </div>
      </div>
    );
  }


  return (
    <Tabs 
      defaultValue="capture" 
      className="relative flex h-screen w-full flex-col bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800"
    >
      <Header />
      
      {/* Session Info Banner */}
      <div className="bg-blue-50 dark:bg-blue-900/30 py-2 px-4 border-b border-blue-100 dark:border-blue-800">
        <div className="container flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div className="text-sm">
            <span className="font-medium">Session:</span> 
            <span className="ml-2">Model: {modelNumber}</span>
            <span className="ml-2 hidden sm:inline">|</span>
            <span className="ml-0 sm:ml-2 block sm:inline">Machine: {machineSerialNumber}</span>
            <span className="ml-0 sm:ml-2 block sm:inline">Inspector: {inspectorName}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
              Frame ID: {frameId?.substring(0, 8)}...
            </span>
            <SessionEditor compact={true} />
            <button
              onClick={() => setShowSessionEditor(true)}
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              Edit Session
            </button>
            <Link 
              href="/"
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
            >
              Change Session
            </Link>
          </div>
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden">
        <CaptureUpdatesProvider>
          <TabsContent value="capture" className="m-0 h-full">
            <div className="container mx-auto p-0 sm:p-4 h-full">
              <div className="flex flex-col lg:flex-row h-full gap-4">
                {/* Left Panel - Camera */}
                <div className="w-full lg:w-3/5 flex items-center justify-center">
                  <div className="aspect-square w-full max-w-[min(100%,calc(100vh-12rem))] bg-gray-900 rounded-lg overflow-hidden flex flex-col">
                    <CameraPanel />
                  </div>
                </div>
                
                {/* Right Panel - History */}
                <div className="w-full lg:w-2/5 h-[50vh] lg:h-auto bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col">
                  <HistoryPanel 
                    frameId={frameId || ''} 
                    key={frameId}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </CaptureUpdatesProvider>
        
        <TabsContent value="upload" className="m-0 h-full">
          <CaptureUpdatesProvider>
            <UploadPanel />
          </CaptureUpdatesProvider>
        </TabsContent>
        
        <TabsContent value="live" className="m-0 h-full">
          <div className="container mx-auto p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
              <h2 className="text-xl font-semibold mb-4">Live Detection Mode</h2>
              <p>Live detection functionality will be implemented here.</p>
            </div>
          </div>
        </TabsContent>
      </div>

      {/* Session Editor Dialog */}
      <Dialog open={showSessionEditor} onOpenChange={setShowSessionEditor}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Edit Session</DialogTitle>
          </DialogHeader>
          <SessionEditor onClose={() => setShowSessionEditor(false)} />
        </DialogContent>
      </Dialog>
    </Tabs>
  );
}