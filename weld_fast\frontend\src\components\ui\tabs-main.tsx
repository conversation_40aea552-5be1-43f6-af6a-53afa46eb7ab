"use client";

import { TabsContent } from "@/components/ui/tabs";
import { Camera, Upload, Wifi } from "lucide-react";

export function TabsMainContent() {
  return (
    <>
      <TabsContent value="capture" className="mt-6">
        <div className="overflow-hidden rounded-xl border bg-gradient-to-b from-muted/50 to-muted p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-blue-100 p-6 text-blue-700">
              <Camera className="h-12 w-12" />
            </div>
            <h2 className="text-2xl font-semibold tracking-tight">Capture Mode</h2>
            <p className="text-center text-muted-foreground">
              Take photos with your camera to detect objects in real-time.
            </p>
          </div>
        </div>
      </TabsContent>
      <TabsContent value="upload" className="mt-6">
        <div className="overflow-hidden rounded-xl border bg-gradient-to-b from-muted/50 to-muted p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-indigo-100 p-6 text-indigo-700">
              <Upload className="h-12 w-12" />
            </div>
            <h2 className="text-2xl font-semibold tracking-tight">Upload Mode</h2>
            <p className="text-center text-muted-foreground">
              Upload images to detect and analyze objects within them.
            </p>
          </div>
        </div>
      </TabsContent>
      <TabsContent value="live" className="mt-6">
        <div className="overflow-hidden rounded-xl border bg-gradient-to-b from-muted/50 to-muted p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-blue-100 p-6 text-blue-700">
              <Wifi className="h-12 w-12" />
            </div>
            <h2 className="text-2xl font-semibold tracking-tight">Live Detection</h2>
            <p className="text-center text-muted-foreground">
              Real-time object detection using your camera feed.
            </p>
          </div>
        </div>
      </TabsContent>
    </>
  );
} 