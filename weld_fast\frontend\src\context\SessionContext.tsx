'use client';

import { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { Frame } from '@/lib/db/types';
import { getFrameById } from '@/lib/db/frameOperations';
import { useAuth } from './AuthContext';

interface SessionContextType {
  frameId: string | null;
  modelNumber: string;
  machineSerialNumber: string;
  inspectorName: string;
  currentFrame: Frame | null;
  isLoading: boolean;
  setSessionInfo: (info: { 
    modelNumber: string; 
    machineSerialNumber: string; 
    inspectorName: string;
    frameId?: string;
  }) => void;
  setCurrentFrame: (frame: Frame | null) => void;
  loadFrame: (frameId: string) => Promise<void>;
  clearSession: () => void;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [sessionInfo, setSessionInfo] = useState({
    frameId: null as string | null,
    modelNumber: '',
    machineSerialNumber: '',
    inspectorName: ''
  });
  const [currentFrame, setCurrentFrame] = useState<Frame | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Update inspector name when user changes
  useEffect(() => {
    if (user && isAuthenticated) {
      setSessionInfo(prev => ({
        ...prev,
        inspectorName: user.full_name
      }));
    }
  }, [user, isAuthenticated]);

  const updateSessionInfo = (info: { 
    modelNumber: string; 
    machineSerialNumber: string; 
    inspectorName: string;
    frameId?: string;
  }) => {
    setSessionInfo(prev => ({
      ...prev,
      modelNumber: info.modelNumber,
      machineSerialNumber: info.machineSerialNumber,
      inspectorName: info.inspectorName,
      frameId: info.frameId || prev.frameId
    }));
  };

  const loadFrame = useCallback(async (frameId: string) => {
    setIsLoading(true);
    try {
      const frame = await getFrameById(frameId);
      if (frame) {
        setCurrentFrame(frame);
        setSessionInfo(prev => ({
          ...prev,
          frameId: frame.frameId,
          modelNumber: frame.modelNumber,
          machineSerialNumber: frame.machineSerialNumber,
          inspectorName: frame.inspectorName
        }));
      }
    } catch (error) {
      console.error('Error loading frame:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearSession = () => {
    setSessionInfo({
      frameId: null,
      modelNumber: '',
      machineSerialNumber: '',
      inspectorName: ''
    });
    setCurrentFrame(null);
  };

  // Load frame when frameId changes
  useEffect(() => {
    if (sessionInfo.frameId && !currentFrame) {
      loadFrame(sessionInfo.frameId);
    }
  }, [sessionInfo.frameId, currentFrame, loadFrame]);

  return (
    <SessionContext.Provider value={{ 
      ...sessionInfo,
      currentFrame,
      isLoading,
      setSessionInfo: updateSessionInfo,
      setCurrentFrame,
      loadFrame,
      clearSession
    }}>
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}