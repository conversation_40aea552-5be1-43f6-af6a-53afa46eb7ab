#!/usr/bin/env python3
"""Simple test script to verify API endpoints work."""

import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_api():
    """Test the main API endpoints."""
    async with aiohttp.ClientSession() as session:
        print("🔧 Testing Weld Detection API Endpoints\n")
        
        # Test 1: Check if server is running
        try:
            async with session.get(f"{BASE_URL}/") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Server is running: {result['message']}")
                else:
                    print(f"❌ Server returned status {response.status}")
                    return
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            print("Make sure to start the server with: uvicorn app.main:app --reload")
            return
        
        # Test 2: Try to access protected endpoint without auth (should fail)
        try:
            async with session.get(f"{BASE_URL}/api/v1/frames/") as response:
                if response.status == 401:
                    print("✅ Authentication is working (401 for unauth request)")
                else:
                    print(f"⚠️  Expected 401, got {response.status}")
        except Exception as e:
            print(f"❌ Error testing auth: {e}")
        
        # Test 3: Login and get token
        login_data = {
            "username": "inspector1",
            "password": "password123"
        }
        
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=login_data
            ) as response:
                if response.status == 200:
                    token_response = await response.json()
                    access_token = token_response["access_token"]
                    print("✅ Login successful, got access token")
                else:
                    error = await response.text()
                    print(f"❌ Login failed: {response.status} - {error}")
                    return
        except Exception as e:
            print(f"❌ Login error: {e}")
            return
        
        # Test 4: Access protected endpoint with token
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            async with session.get(f"{BASE_URL}/api/v1/frames/", headers=headers) as response:
                if response.status == 200:
                    frames_response = await response.json()
                    print(f"✅ Frames endpoint accessible: {frames_response['total']} frames found")
                else:
                    error = await response.text()
                    print(f"❌ Frames endpoint failed: {response.status} - {error}")
        except Exception as e:
            print(f"❌ Frames endpoint error: {e}")
        
        # Test 5: Create a new frame
        frame_data = {
            "model_number": "WELD-001",
            "machine_serial_number": "SN12345",
            "inspector_name": "inspector1",
            "status": "active",
            "metadata": {"test": True, "created_by": "api_test"}
        }
        
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/frames/",
                headers=headers,
                json=frame_data
            ) as response:
                if response.status == 200:
                    new_frame = await response.json()
                    frame_id = new_frame["frame_id"]
                    print(f"✅ Frame created successfully: {frame_id}")
                    
                    # Test 6: Get the created frame
                    async with session.get(f"{BASE_URL}/api/v1/frames/{frame_id}", headers=headers) as get_response:
                        if get_response.status == 200:
                            frame_details = await get_response.json()
                            print(f"✅ Frame retrieved: {frame_details['model_number']}")
                        else:
                            print(f"❌ Failed to retrieve frame: {get_response.status}")
                            
                else:
                    error = await response.text()
                    print(f"❌ Frame creation failed: {response.status} - {error}")
        except Exception as e:
            print(f"❌ Frame creation error: {e}")
        
        print("\n🎉 API testing completed!")

if __name__ == "__main__":
    asyncio.run(test_api())