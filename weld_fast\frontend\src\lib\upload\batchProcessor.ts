// Batch processing manager for upload functionality
import * as tf from '@tensorflow/tfjs';
import { createCapture } from '@/lib/db/captureOperations';
import { detect, createDetectionCanvas, PerformanceMetrics } from '@/lib/detection';
import { processImageForDetection, ImageProcessingError, canvasToBlob } from './imageProcessing';
import { UploadFile } from '@/components/detection/FileDropZone';

export interface BatchProcessingConfig {
  concurrentLimit: number;
  confidenceThreshold: number;
  iouThreshold: number;
  maxDetections: number;
}

export interface ProcessingResult {
  fileId: string;
  success: boolean;
  captureId?: string;
  detectionCount?: number;
  error?: string;
  processingTime?: number;
  performance?: PerformanceMetrics;
}

export interface BatchProgress {
  total: number;
  completed: number;
  failed: number;
  processing: number;
  percentage: number;
  currentFile?: string;
}

export type ProgressCallback = (progress: BatchProgress) => void;
export type FileStatusCallback = (fileId: string, status: 'processing' | 'completed' | 'error', result?: ProcessingResult) => void;

export class BatchProcessor {
  private model: tf.GraphModel | null = null;
  private frameId: string;
  private config: BatchProcessingConfig;
  private isProcessing = false;
  private abortController: AbortController | null = null;

  constructor(
    model: tf.GraphModel | null,
    frameId: string,
    config: Partial<BatchProcessingConfig> = {}
  ) {
    this.model = model;
    this.frameId = frameId;
    this.config = {
      concurrentLimit: 3,
      confidenceThreshold: 0.25,
      iouThreshold: 0.45,
      maxDetections: 100,
      ...config
    };
  }

  updateModel(model: tf.GraphModel | null): void {
    this.model = model;
  }

  updateFrameId(frameId: string): void {
    this.frameId = frameId;
  }

  isProcessingBatch(): boolean {
    return this.isProcessing;
  }

  async processBatch(
    files: UploadFile[],
    onProgress: ProgressCallback,
    onFileStatus: FileStatusCallback
  ): Promise<ProcessingResult[]> {
    if (!this.model) {
      throw new Error('AI model not loaded');
    }

    if (!this.frameId) {
      throw new Error('No active session frame');
    }

    if (this.isProcessing) {
      throw new Error('Batch processing already in progress');
    }

    this.isProcessing = true;
    this.abortController = new AbortController();

    const results: ProcessingResult[] = [];
    const progress: BatchProgress = {
      total: files.length,
      completed: 0,
      failed: 0,
      processing: 0,
      percentage: 0
    };

    try {
      // Process files in batches with concurrency limit
      const batches = this.createBatches(files, this.config.concurrentLimit);
      
      for (const batch of batches) {
        if (this.abortController.signal.aborted) {
          break;
        }

        const batchPromises = batch.map(file => this.processFile(file, onFileStatus));
        const batchResults = await Promise.allSettled(batchPromises);

        for (let i = 0; i < batchResults.length; i++) {
          const result = batchResults[i];
          
          if (result.status === 'fulfilled') {
            results.push(result.value);
            if (result.value.success) {
              progress.completed++;
            } else {
              progress.failed++;
            }
          } else {
            const file = batch[i];
            const errorResult: ProcessingResult = {
              fileId: file.id,
              success: false,
              error: result.reason?.message || 'Unknown processing error'
            };
            results.push(errorResult);
            progress.failed++;
            onFileStatus(file.id, 'error', errorResult);
          }

          progress.percentage = Math.round(((progress.completed + progress.failed) / progress.total) * 100);
          onProgress(progress);
        }
      }

      return results;
    } catch (error) {
      throw error;
    } finally {
      this.isProcessing = false;
      this.abortController = null;
    }
  }

  abortProcessing(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processFile(
    uploadFile: UploadFile,
    onFileStatus: FileStatusCallback
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      if (this.abortController?.signal.aborted) {
        throw new Error('Processing aborted');
      }

      onFileStatus(uploadFile.id, 'processing');

      // Step 1: Process image for detection
      const {
        detectionCanvas,
        originalBlob
      } = await processImageForDetection(uploadFile.file);

      if (this.abortController?.signal.aborted) {
        throw new Error('Processing aborted');
      }

      // Step 2: Perform AI detection
      const { detections, metrics } = await detect(detectionCanvas, this.model!, {
        confidenceThreshold: this.config.confidenceThreshold,
        iouThreshold: this.config.iouThreshold,
        maxDetections: this.config.maxDetections,
        inputSize: 640
      });

      if (this.abortController?.signal.aborted) {
        throw new Error('Processing aborted');
      }

      // Step 3: Create processed image with detection overlays
      const processedCanvas = createDetectionCanvas(detectionCanvas, detections, {
        confidenceThreshold: this.config.confidenceThreshold,
        showConfidence: true,
        showLabels: true
      });

      const processedBlob = await canvasToBlob(processedCanvas, 0.9);

      if (this.abortController?.signal.aborted) {
        throw new Error('Processing aborted');
      }

      // Step 4: Save to IndexedDB
      const capture = await createCapture(
        this.frameId,
        originalBlob,
        processedBlob,
        detections
      );

      const processingTime = Date.now() - startTime;
      const result: ProcessingResult = {
        fileId: uploadFile.id,
        success: true,
        captureId: capture.captureId,
        detectionCount: detections.length,
        processingTime,
        performance: metrics
      };

      onFileStatus(uploadFile.id, 'completed', result);
      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      let errorMessage = 'Unknown processing error';

      if (error instanceof ImageProcessingError) {
        errorMessage = error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      const result: ProcessingResult = {
        fileId: uploadFile.id,
        success: false,
        error: errorMessage,
        processingTime
      };

      onFileStatus(uploadFile.id, 'error', result);
      return result;
    }
  }
}

// Utility function to create batch processor instance
export function createBatchProcessor(
  model: tf.GraphModel | null,
  frameId: string,
  config?: Partial<BatchProcessingConfig>
): BatchProcessor {
  return new BatchProcessor(model, frameId, config);
}

// Utility function to estimate processing time
export function estimateProcessingTime(fileCount: number, concurrentLimit: number = 3): {
  estimatedSeconds: number;
  estimatedMinutes: number;
  description: string;
} {
  // Average processing time per file: ~3-5 seconds
  const avgTimePerFile = 4;
  const parallelBatches = Math.ceil(fileCount / concurrentLimit);
  const estimatedSeconds = parallelBatches * avgTimePerFile;
  const estimatedMinutes = Math.ceil(estimatedSeconds / 60);

  let description = '';
  if (estimatedSeconds < 60) {
    description = `~${estimatedSeconds} seconds`;
  } else if (estimatedMinutes === 1) {
    description = '~1 minute';
  } else {
    description = `~${estimatedMinutes} minutes`;
  }

  return {
    estimatedSeconds,
    estimatedMinutes,
    description
  };
}