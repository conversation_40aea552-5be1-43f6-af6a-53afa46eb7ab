{"routes": [{"route": "/api/*", "allowedRoles": ["anonymous"]}, {"route": "/login", "allowedRoles": ["anonymous"]}, {"route": "/register", "allowedRoles": ["anonymous"]}, {"route": "/unauthorized", "allowedRoles": ["anonymous"]}, {"route": "/detection", "allowedRoles": ["authenticated"]}, {"route": "/profile", "allowedRoles": ["authenticated"]}, {"route": "/*", "rewrite": "/index.html"}], "responseOverrides": {"401": {"redirect": "/login", "statusCode": 302}, "403": {"redirect": "/unauthorized", "statusCode": 302}, "404": {"rewrite": "/index.html", "statusCode": 200}}, "globalHeaders": {"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"}, "mimeTypes": {".json": "application/json", ".js": "application/javascript", ".css": "text/css", ".html": "text/html", ".png": "image/png", ".jpg": "image/jpeg", ".jpeg": "image/jpeg", ".gif": "image/gif", ".svg": "image/svg+xml", ".ico": "image/x-icon", ".woff": "font/woff", ".woff2": "font/woff2", ".ttf": "font/ttf", ".eot": "application/vnd.ms-fontobject", ".webmanifest": "application/manifest+json", ".bin": "application/octet-stream"}, "navigationFallback": {"rewrite": "/index.html", "exclude": ["/models/*", "/icons/*", "/workers/*", "/*.{js,css,png,jpg,jpeg,gif,svg,ico,woff,woff2,ttf,eot,json,bin}", "/sw.js", "/manifest.json"]}, "platform": {"apiRuntime": "node:18"}, "trailingSlash": "auto"}